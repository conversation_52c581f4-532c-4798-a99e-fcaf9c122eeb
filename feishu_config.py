#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
飞书API配置文件
"""

# 飞书应用配置
FEISHU_CONFIG = {
    "app_id": "cli_a828491ea031d013",
    "app_secret": "",  # 请填入您的应用密钥
    
    # 多维表格配置
    "base_url": "https://gwvwal7cgy.feishu.cn/base/TtULb7pBiaGRMgs4dfac4aLAnId",
    "app_token": "TtULb7pBiaGRMgs4dfac4aLAnId",  # 从URL中提取的app_token
    "table_id": "",  # 需要获取具体的table_id
    
    # API端点
    "api_base": "https://open.feishu.cn/open-apis",
    "auth_url": "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
}

# 字段映射配置（根据实际多维表格字段）
FIELD_MAPPING = {
    "发送时间": "发送时间",        # 日期格式
    "发送人": "发送人",            # 文本格式
    "群聊名称": "群聊名称",        # 文本格式
    "消息内容": "消息内容",        # 文本格式
    "消息ID": "消息ID",           # 文本格式（当前消息的ID）
    "消息附件": "消息附件",        # 附件格式
    "消息类型": "消息类型",        # 文本格式
    "被引用消息ID": "被引用消息ID" # 文本格式（引用的消息ID）
}

# 微信RPC配置
WECHAT_CONFIG = {
    "host": "http://127.0.0.1:50007",
    "pid": "22504",
    "poll_interval": 3  # 轮询间隔（秒）
}
