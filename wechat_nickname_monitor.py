#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信群消息监控程序（带真实昵称）
功能：实时监控微信群消息，显示发送人真实昵称、发送时间、群聊名称、消息内容
"""

import requests
import json
import time
import datetime
import re
import base64

def main():
    print("=" * 70)
    print("微信群消息监控程序（带真实昵称）")
    print("功能：实时监控微信群消息，显示发送人真实昵称")
    print("=" * 70)
    
    host = "http://127.0.0.1:50007"
    pid = "22504"
    headers = {"X-WeChat-PID": str(pid)}
    
    try:
        # 获取数据库句柄
        print("正在连接数据库...")
        response = requests.post(f"{host}/api/db/getDBInfo", headers=headers, timeout=10)
        data = response.json()
        
        msg_handle = None
        micromsg_handle = None
        
        for db in data["Data"]:
            if db.get("databaseName") == "MSG0.db":
                msg_handle = db.get("handle")
            elif "MicroMsg" in db.get("databaseName", ""):
                micromsg_handle = db.get("handle")
        
        if not all([msg_handle, micromsg_handle]):
            print("❌ 数据库连接失败")
            return
        
        print(f"✅ 数据库连接成功")
        
        # 加载联系人信息
        print("正在加载联系人信息...")
        sql_contact = "SELECT UserName, NickName, Remark FROM Contact;"
        data_contact = {
            "dbHandle": str(micromsg_handle),
            "sql": sql_contact
        }
        
        contact_cache = {}
        response_contact = requests.post(f"{host}/api/db/execSql", headers=headers, json=data_contact, timeout=10)
        result_contact = response_contact.json()
        
        if result_contact.get("Data"):
            for row in result_contact["Data"][1:]:  # 跳过列名
                if len(row) >= 3:
                    username = row[0]
                    nickname = row[1] if row[1] else username
                    remark = row[2] if row[2] else ""
                    
                    display_name = remark if remark else nickname
                    contact_cache[username] = display_name
        
        print(f"✅ 加载了 {len(contact_cache)} 个联系人信息")
        
        # 获取群聊列表
        print("正在获取群聊列表...")
        response_groups = requests.post(f"{host}/api/session/getSessionList", headers=headers, timeout=10)
        data_groups = response_groups.json()
        
        groups = []
        if data_groups.get("Data"):
            groups = [s for s in data_groups["Data"] if s.get("wxid", "").endswith("@chatroom")]
        
        print(f"✅ 找到 {len(groups)} 个群聊")
        
        if not groups:
            print("❌ 未找到群聊")
            return
        
        # 显示监控的群聊
        print("\n监控的群聊:")
        for i, group in enumerate(groups[:5], 1):
            group_name = contact_cache.get(group.get('wxid'), group.get('nickname', group.get('wxid')))
            print(f"  {i}. {group_name}")
        if len(groups) > 5:
            print(f"  ... 还有 {len(groups) - 5} 个群聊")
        
        # 获取当前最新消息ID作为起始点
        print("\n正在初始化...")
        sql_init = "SELECT MAX(localId) as maxId FROM MSG WHERE StrTalker LIKE '%@chatroom';"
        data_init = {
            "dbHandle": str(msg_handle),
            "sql": sql_init
        }
        
        response_init = requests.post(f"{host}/api/db/execSql", headers=headers, json=data_init, timeout=10)
        result_init = response_init.json()
        
        last_msg_id = 0
        if result_init.get("Data") and len(result_init["Data"]) > 1:
            for row in result_init["Data"][1:]:
                if row and len(row) > 0 and str(row[0]).isdigit():
                    last_msg_id = int(row[0])
                    break
        
        print(f"✅ 初始化完成，最新消息ID: {last_msg_id}")
        print(f"\n🚀 开始实时监控（每3秒检查一次）...")
        print("=" * 70)
        
        # 开始监控循环
        while True:
            try:
                # 查询新消息
                sql_new = f"""
                SELECT localId, TalkerId, CreateTime, StrTalker, StrContent, IsSender, Type, BytesExtra
                FROM MSG 
                WHERE localId > {last_msg_id}
                AND StrTalker LIKE '%@chatroom'
                ORDER BY localId ASC;
                """
                
                data_new = {
                    "dbHandle": str(msg_handle),
                    "sql": sql_new
                }
                
                response_new = requests.post(f"{host}/api/db/execSql", headers=headers, json=data_new, timeout=10)
                result_new = response_new.json()
                
                if result_new.get("Data") and len(result_new["Data"]) > 1:
                    for row in result_new["Data"][1:]:  # 跳过列名
                        if len(row) >= 8 and str(row[0]).isdigit():
                            local_id = int(row[0])
                            create_time = int(row[2]) if str(row[2]).isdigit() else 0
                            group_id = row[3]
                            content = row[4]
                            is_sender = row[5]
                            msg_type = row[6]
                            bytes_extra = row[7]
                            
                            # 格式化时间
                            try:
                                send_time = datetime.datetime.fromtimestamp(create_time).strftime("%Y-%m-%d %H:%M:%S")
                            except:
                                send_time = "未知时间"
                            
                            # 获取群名称
                            group_name = contact_cache.get(group_id, group_id)
                            
                            # 获取发送者信息
                            if str(is_sender) == "1":
                                sender_name = "我"
                            else:
                                # 从BytesExtra中提取发送者微信ID
                                sender_wxid = None
                                if bytes_extra:
                                    try:
                                        bytes_str = str(bytes_extra)
                                        
                                        # 尝试base64解码
                                        try:
                                            decoded = base64.b64decode(bytes_str).decode('utf-8', errors='ignore')
                                            wxid_pattern = r'wxid_[a-zA-Z0-9]+'
                                            match = re.search(wxid_pattern, decoded)
                                            if match:
                                                sender_wxid = match.group(0)
                                        except:
                                            pass
                                        
                                        # 如果base64解码失败，直接查找
                                        if not sender_wxid:
                                            wxid_pattern = r'wxid_[a-zA-Z0-9]+'
                                            match = re.search(wxid_pattern, bytes_str)
                                            if match:
                                                sender_wxid = match.group(0)
                                    except:
                                        pass
                                
                                if sender_wxid:
                                    sender_name = contact_cache.get(sender_wxid, sender_wxid)
                                else:
                                    sender_name = "未知用户"
                            
                            # 构建并输出消息
                            formatted_msg = {
                                "发送人": sender_name,
                                "发送时间": send_time,
                                "群聊名称": group_name,
                                "消息内容": content,
                                "消息类型": str(msg_type),
                                "消息ID": str(local_id)
                            }
                            
                            print(json.dumps(formatted_msg, ensure_ascii=False, indent=2))
                            print("-" * 30)
                            
                            # 更新最新消息ID
                            last_msg_id = local_id
                
                # 等待3秒
                time.sleep(3)
                
            except KeyboardInterrupt:
                print("\n⏹️  监控已停止")
                break
            except requests.exceptions.RequestException as e:
                print(f"❌ 网络请求错误: {e}")
                time.sleep(5)
            except Exception as e:
                print(f"❌ 处理消息时出错: {e}")
                time.sleep(5)
                
    except Exception as e:
        print(f"❌ 程序初始化错误: {e}")

if __name__ == "__main__":
    main()
