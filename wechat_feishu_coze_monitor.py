#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信群消息监控 + 飞书上传 + Coze自动回复 - 正式版
功能：实时监控微信群消息，上传到飞书多维表格，并通过Coze自动回复
"""

import requests
import json
import time
import datetime
import re
import base64
import os

# 配置信息
CONFIG = {
    # 微信RPC配置
    "wechat": {
        "host": "http://127.0.0.1:50007",
        "pid": "22504",
        "poll_interval": 3
    },
    
    # 飞书配置
    "feishu": {
        "app_id": "cli_a828491ea031d013",
        "app_secret": "",  # 需要从飞书开放平台获取应用密钥
        "app_token": "TtULb7pBiaGRMgs4dfac4aLAnId",
        "table_id": "",    # 需要从多维表格获取表格ID
        "api_base": "https://open.feishu.cn/open-apis"
    },

    # Coze配置
    "coze": {
        "api_url": "https://api.coze.cn/open_api/v2/chat",  # Coze API地址
        "api_key": "",     # 需要填入Coze API密钥
        "bot_id": ""       # 需要填入Bot ID
    }
}

class FeishuAPI:
    def __init__(self, app_id, app_secret):
        self.app_id = app_id
        self.app_secret = app_secret
        self.access_token = None
        self.token_expires_at = 0
    
    def get_access_token(self):
        """获取飞书访问令牌"""
        if self.access_token and time.time() < self.token_expires_at:
            return self.access_token
        
        url = f"{CONFIG['feishu']['api_base']}/auth/v3/tenant_access_token/internal"
        payload = {
            "app_id": self.app_id,
            "app_secret": self.app_secret
        }
        
        try:
            response = requests.post(url, json=payload, timeout=10)
            data = response.json()
            
            if data.get("code") == 0:
                self.access_token = data["tenant_access_token"]
                self.token_expires_at = time.time() + data.get("expire", 3600) - 300
                return self.access_token
            else:
                print(f"❌ 获取飞书访问令牌失败: {data}")
                return None
        except Exception as e:
            print(f"❌ 获取飞书访问令牌异常: {e}")
            return None
    
    def get_first_table_id(self, app_token):
        """获取第一个表格的ID"""
        access_token = self.get_access_token()
        if not access_token:
            return None

        url = f"{CONFIG['feishu']['api_base']}/bitable/v1/apps/{app_token}/tables"
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }

        try:
            response = requests.get(url, headers=headers, timeout=10)
            data = response.json()

            if data.get("code") == 0:
                tables = data.get("data", {}).get("items", [])
                if tables:
                    table_id = tables[0]["table_id"]
                    table_name = tables[0]["name"]
                    print(f"✅ 自动获取到表格: {table_name} (ID: {table_id})")
                    return table_id
            return None
        except Exception as e:
            print(f"❌ 获取表格ID异常: {e}")
            return None

    def add_record_to_table(self, app_token, table_id, fields):
        """向多维表格添加记录"""
        access_token = self.get_access_token()
        if not access_token:
            return False

        url = f"{CONFIG['feishu']['api_base']}/bitable/v1/apps/{app_token}/tables/{table_id}/records"
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }

        payload = {"fields": fields}

        try:
            response = requests.post(url, headers=headers, json=payload, timeout=10)
            data = response.json()

            if data.get("code") == 0:
                return True
            else:
                print(f"❌ 飞书上传失败: {data}")
                return False
        except Exception as e:
            print(f"❌ 飞书上传异常: {e}")
            return False

class CozeAPI:
    def __init__(self, api_url, api_key, bot_id):
        self.api_url = api_url
        self.api_key = api_key
        self.bot_id = bot_id

    def get_reply(self, message, user_id="user"):
        """获取Coze回复"""
        if not all([self.api_url, self.api_key, self.bot_id]):
            return None

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        payload = {
            "conversation_id": f"wechat_{user_id}_{int(time.time())}",
            "bot_id": self.bot_id,
            "user": user_id,
            "query": message,
            "stream": False
        }

        try:
            response = requests.post(self.api_url, headers=headers, json=payload, timeout=30)
            data = response.json()

            if data.get("code") == 0:
                messages = data.get("messages", [])
                for msg in messages:
                    if msg.get("type") == "answer":
                        return msg.get("content", "")
            else:
                print(f"❌ Coze API错误: {data}")
            return None
        except Exception as e:
            print(f"❌ Coze API异常: {e}")
            return None

def extract_sender_wxid(bytes_extra):
    """从BytesExtra中提取发送者微信ID"""
    if not bytes_extra:
        return None
    
    try:
        bytes_str = str(bytes_extra)
        
        # 尝试base64解码
        try:
            decoded = base64.b64decode(bytes_str).decode('utf-8', errors='ignore')
            wxid_pattern = r'wxid_[a-zA-Z0-9]+'
            match = re.search(wxid_pattern, decoded)
            if match:
                return match.group(0)
        except:
            pass
        
        # 直接查找
        wxid_pattern = r'wxid_[a-zA-Z0-9]+'
        match = re.search(wxid_pattern, bytes_str)
        if match:
            return match.group(0)
        
        return None
    except:
        return None

def is_quote_message(msg):
    """判断是否为引用消息"""
    return (
        str(msg.get("type")) == "49" and 
        str(msg.get("subType")) in ["51", "57"] and 
        msg.get("compressContent") is not None
    )

def parse_quote_info(compress_content):
    """解析引用消息信息"""
    if not compress_content:
        return None
    
    try:
        decoded = base64.b64decode(compress_content).decode('utf-8', errors='ignore')
        svrid_matches = re.findall(r'\b(\d{15,20})\b', decoded)
        if svrid_matches:
            return svrid_matches[0]
        return None
    except Exception as e:
        return None

def format_for_feishu_table(msg_data):
    """格式化为飞书多维表格字段"""
    quote_id = ""
    if msg_data.get("引用信息") and msg_data["引用信息"]:
        quote_id = msg_data["引用信息"].get("被引用消息ID", "")
    
    # 处理消息附件描述
    msg_type = msg_data.get("消息类型", "")
    attachment_desc = ""
    if msg_type == "3":
        attachment_desc = "图片"
    elif msg_type == "43":
        attachment_desc = "视频"
    elif msg_type == "34":
        attachment_desc = "语音"
    elif msg_type == "49":
        attachment_desc = "文件/链接"
    
    # 飞书多维表格字段
    feishu_record = {
        "发送时间": msg_data.get("发送时间", ""),
        "发送人": msg_data.get("发送人", ""),
        "群聊名称": msg_data.get("群聊名称", ""),
        "消息内容": msg_data.get("消息内容", ""),
        "消息ID": msg_data.get("消息ID", ""),
        "消息附件": attachment_desc,
        "消息类型": msg_data.get("消息类型", ""),
        "被引用消息ID": quote_id
    }
    
    return feishu_record

def send_wechat_message(content, group_id, headers):
    """发送微信消息"""
    url = f"{CONFIG['wechat']['host']}/api/msg/sendTextMsg"
    payload = {
        "wxid": group_id,
        "msg": content
    }

    try:
        response = requests.post(url, headers=headers, json=payload, timeout=10)
        return response.status_code == 200
    except Exception as e:
        print(f"❌ 发送微信消息失败: {e}")
        return False

def should_auto_reply(msg_data):
    """判断是否需要自动回复"""
    # 不回复自己的消息
    if msg_data.get("发送人") == "我":
        return False
    
    # 只回复文本消息
    if msg_data.get("消息类型") != "1":
        return False
    
    content = msg_data.get("消息内容", "").strip()
    
    # 消息内容不为空且长度大于2
    if len(content) > 2:
        return True
    
    return False

def main():
    print("=" * 80)
    print("微信群消息监控 + 飞书上传 + Coze自动回复 - 正式版")
    print("=" * 80)
    
    # 检查配置
    if not CONFIG["feishu"]["app_secret"]:
        print("⚠️  飞书应用密钥未配置，将跳过飞书上传功能")
        print("   请访问 https://open.feishu.cn/ 获取应用密钥")

    if not all([CONFIG["coze"]["api_url"], CONFIG["coze"]["api_key"], CONFIG["coze"]["bot_id"]]):
        print("⚠️  Coze配置未完整，将跳过自动回复功能")
        print("   需要配置：API地址、API密钥、Bot ID")
    else:
        print("✅ Coze自动回复功能已启用")

    # 初始化API
    feishu_api = None
    if CONFIG["feishu"]["app_secret"]:
        feishu_api = FeishuAPI(CONFIG["feishu"]["app_id"], CONFIG["feishu"]["app_secret"])

        # 如果没有配置table_id，尝试自动获取
        if not CONFIG["feishu"]["table_id"]:
            table_id = feishu_api.get_first_table_id(CONFIG["feishu"]["app_token"])
            if table_id:
                CONFIG["feishu"]["table_id"] = table_id

    coze_api = None
    if all([CONFIG["coze"]["api_url"], CONFIG["coze"]["api_key"], CONFIG["coze"]["bot_id"]]):
        coze_api = CozeAPI(CONFIG["coze"]["api_url"], CONFIG["coze"]["api_key"], CONFIG["coze"]["bot_id"])
    
    headers = {"X-WeChat-PID": str(CONFIG["wechat"]["pid"])}
    
    try:
        # 获取微信数据库句柄
        print("正在连接微信数据库...")
        response = requests.post(f"{CONFIG['wechat']['host']}/api/db/getDBInfo", headers=headers, timeout=10)
        data = response.json()
        
        msg_handle = None
        micromsg_handle = None
        
        for db in data["Data"]:
            if db.get("databaseName") == "MSG0.db":
                msg_handle = db.get("handle")
            elif "MicroMsg" in db.get("databaseName", ""):
                micromsg_handle = db.get("handle")
        
        if not all([msg_handle, micromsg_handle]):
            print("❌ 微信数据库连接失败")
            return
        
        print(f"✅ 微信数据库连接成功")
        
        # 加载联系人信息
        print("正在加载联系人信息...")
        sql_contact = """
        SELECT UserName, NickName, Remark FROM Contact 
        WHERE UserName LIKE '%@chatroom' OR UserName LIKE 'wxid_%'
        LIMIT 200;
        """
        data_contact = {"dbHandle": str(micromsg_handle), "sql": sql_contact}
        
        contact_cache = {}
        try:
            response_contact = requests.post(f"{CONFIG['wechat']['host']}/api/db/execSql", headers=headers, json=data_contact, timeout=15)
            result_contact = response_contact.json()
            
            if result_contact.get("Data"):
                for row in result_contact["Data"][1:]:
                    if len(row) >= 3:
                        username = row[0]
                        nickname = row[1] if row[1] else username
                        remark = row[2] if row[2] else ""
                        display_name = remark if remark else nickname
                        contact_cache[username] = display_name
            
            print(f"✅ 加载了 {len(contact_cache)} 个联系人信息")
        except Exception as e:
            print(f"⚠️  联系人加载失败，使用基础缓存: {e}")
            contact_cache = {
                "wxid_44ryxbf3scuq22": "小张",
                "wxid_0f9dl5advg6222": "Wonder", 
                "wxid_i18pfbai664r22": "我来收集总结你们的聊天(bot)",
                "56347303221@chatroom": "测试群",
                "46113402449@chatroom": "业务AI化小组"
            }
        
        # 获取当前最新消息ID作为起始点
        print("正在初始化...")
        sql_init = "SELECT MAX(localId) as maxId FROM MSG WHERE StrTalker LIKE '%@chatroom';"
        data_init = {"dbHandle": str(msg_handle), "sql": sql_init}
        
        response_init = requests.post(f"{CONFIG['wechat']['host']}/api/db/execSql", headers=headers, json=data_init, timeout=10)
        result_init = response_init.json()
        
        last_msg_id = 0
        if result_init.get("Data") and len(result_init["Data"]) > 1:
            for row in result_init["Data"][1:]:
                if row and len(row) > 0 and str(row[0]).isdigit():
                    last_msg_id = int(row[0])
                    break
        
        print(f"✅ 初始化完成，最新消息ID: {last_msg_id}")
        print(f"\n🚀 开始实时监控（每{CONFIG['wechat']['poll_interval']}秒检查一次）...")
        print("=" * 80)
        
        # 开始监控循环
        while True:
            try:
                # 查询新消息
                sql_new = f"""
                SELECT localId, TalkerId, CreateTime, StrTalker, StrContent, IsSender, Type, SubType, BytesExtra, CompressContent, MsgSvrID
                FROM MSG 
                WHERE localId > {last_msg_id}
                AND StrTalker LIKE '%@chatroom'
                ORDER BY localId ASC;
                """
                
                data_new = {"dbHandle": str(msg_handle), "sql": sql_new}
                response_new = requests.post(f"{CONFIG['wechat']['host']}/api/db/execSql", headers=headers, json=data_new, timeout=10)
                result_new = response_new.json()
                
                if result_new.get("Data") and len(result_new["Data"]) > 1:
                    for row in result_new["Data"][1:]:
                        if len(row) >= 11 and str(row[0]).isdigit():
                            local_id = int(row[0])
                            create_time = int(row[2]) if str(row[2]).isdigit() else 0
                            group_id = row[3]
                            content = row[4]
                            is_sender = row[5]
                            msg_type = row[6]
                            sub_type = row[7]
                            bytes_extra = row[8]
                            compress_content = row[9]
                            msg_svr_id = row[10]
                            
                            # 构建消息对象
                            msg = {
                                "localId": local_id,
                                "createTime": create_time,
                                "talker": group_id,
                                "content": content,
                                "isSender": is_sender,
                                "type": msg_type,
                                "subType": sub_type,
                                "bytesExtra": bytes_extra,
                                "compressContent": compress_content,
                                "msgSvrId": msg_svr_id
                            }
                            
                            # 格式化时间
                            try:
                                send_time = datetime.datetime.fromtimestamp(create_time).strftime("%Y-%m-%d %H:%M:%S")
                            except:
                                send_time = "未知时间"
                            
                            # 获取群名称
                            group_name = contact_cache.get(group_id, group_id)
                            
                            # 获取发送者信息
                            if str(is_sender) == "1":
                                sender_name = "我"
                            else:
                                sender_wxid = extract_sender_wxid(bytes_extra)
                                if sender_wxid:
                                    sender_name = contact_cache.get(sender_wxid, sender_wxid)
                                else:
                                    sender_name = "未知用户"
                            
                            # 检查是否为引用消息
                            quote_info = None
                            if is_quote_message(msg):
                                quote_id = parse_quote_info(compress_content)
                                if quote_id:
                                    quote_info = {"被引用消息ID": quote_id}
                            
                            # 构建消息数据
                            formatted_msg = {
                                "发送人": sender_name,
                                "发送时间": send_time,
                                "群聊名称": group_name,
                                "消息内容": content,
                                "消息类型": str(msg_type),
                                "消息ID": str(msg_svr_id) if msg_svr_id and str(msg_svr_id) != "0" else f"local_{local_id}",
                                "引用信息": quote_info if quote_info else {}
                            }
                            
                            # 输出消息
                            print(f"📱 新消息:")
                            print(json.dumps(formatted_msg, ensure_ascii=False, indent=2))
                            
                            # 上传到飞书
                            if feishu_api and CONFIG["feishu"]["table_id"]:
                                feishu_record = format_for_feishu_table(formatted_msg)
                                if feishu_api.add_record_to_table(CONFIG["feishu"]["app_token"], CONFIG["feishu"]["table_id"], feishu_record):
                                    print("✅ 已上传到飞书")
                                else:
                                    print("❌ 飞书上传失败")
                            
                            # Coze自动回复
                            if coze_api and should_auto_reply(formatted_msg):
                                reply = coze_api.get_reply(content, sender_name)
                                if reply:
                                    if send_wechat_message(reply, group_id, headers):
                                        print(f"🤖 Coze回复: {reply}")
                                    else:
                                        print("❌ 发送回复失败")
                            
                            print("-" * 50)
                            
                            # 更新最新消息ID
                            last_msg_id = local_id
                
                # 等待
                time.sleep(CONFIG["wechat"]["poll_interval"])
                
            except KeyboardInterrupt:
                print("\n⏹️  监控已停止")
                break
            except Exception as e:
                print(f"❌ 处理消息时出错: {e}")
                time.sleep(5)
                
    except Exception as e:
        print(f"❌ 程序初始化错误: {e}")

if __name__ == "__main__":
    main()
