# 微信群消息监控 + 飞书上传 + Coze自动回复 - 正式版

集成微信群消息监控、飞书多维表格上传、Coze AI自动回复的完整解决方案。

## 🎯 唯一正式版本

**`wechat_feishu_coze_monitor.py`** - 集成所有功能的正式版本

### 功能特点
- ✅ 微信群消息实时监控
- ✅ 飞书多维表格自动上传
- ✅ Coze AI自动回复
- ✅ 真实发送人昵称显示
- ✅ 引用消息识别和解析
- ✅ 统一MsgSvrID体系

## 📊 输出格式

### 普通消息
```json
{
  "发送人": "小张",
  "发送时间": "2025-08-19 14:34:13",
  "群聊名称": "测试群",
  "消息内容": "测试消息内容",
  "消息类型": "1",
  "消息ID": "3264915067667192932",
  "引用信息": {}
}
```

### 引用消息
```json
{
  "发送人": "Wonder",
  "发送时间": "2025-08-19 14:25:35",
  "群聊名称": "测试群",
  "消息内容": "",
  "消息类型": "49",
  "消息ID": "2052665057659932943",
  "引用信息": {
    "被引用消息ID": "2640209026829637417"
  }
}
```

## 🚀 快速开始

### 环境要求
- Windows系统
- Python 3.6+
- 微信客户端运行中
- 微信RPC服务运行在127.0.0.1:50007

### 安装依赖
```bash
pip install requests
```

### 启动监控
```bash
python wechat_monitor_final.py
```

## 🔧 测试工具

```bash
# 测试连接
python test_connection.py

# 发送测试消息
python test_send_message.py

# 验证功能
python test_final_function.py
```

## 📁 文件说明

### 核心文件
- **`wechat_monitor_final.py`** - 正式版监控程序（唯一入口）
- **`test_connection.py`** - 连接测试工具
- **`test_send_message.py`** - 消息发送测试工具
- **`test_final_function.py`** - 功能验证工具

### 文档文件
- **`微信群消息监控系统-完整技术文档.md`** - 完整技术文档
- **`项目文件清单.md`** - 项目文件说明
- **`README.md`** - 本文件

## 🔑 技术特点

### 核心突破
1. **发送者识别**：从BytesExtra字段解析真实发送者微信ID
2. **引用消息解析**：从CompressContent字段提取被引用消息ID
3. **ID体系统一**：全部使用MsgSvrID保证跨设备唯一性
4. **实时监控**：基于localId递增的高效增量查询

### 数据来源
- **MSG0.db**：消息数据库（消息内容、发送者信息、引用数据）
- **MicroMsg.db**：联系人数据库（用户昵称、备注信息）

## ⚠️ 注意事项

### 运行要求
- 微信客户端必须保持运行状态
- 微信RPC服务必须运行在指定端口
- 程序会持续运行直到手动停止（Ctrl+C）

### 性能优化
- 联系人信息采用缓存机制
- 3秒轮询间隔平衡实时性和性能
- 只监控群聊消息，过滤无关数据

## 🛠️ 故障排除

### 常见问题
1. **连接失败**：检查微信RPC服务是否运行
2. **无消息输出**：确认群聊有新消息活动
3. **昵称显示为ID**：检查联系人缓存加载情况
4. **引用解析失败**：验证CompressContent字段内容

### 调试步骤
1. 运行 `test_connection.py` 检查连接
2. 运行 `test_send_message.py` 发送测试消息
3. 运行 `test_final_function.py` 验证解析功能
4. 查看程序启动日志确认初始化状态

## 📈 扩展功能

### 可扩展方向
- 消息过滤和关键词监控
- 数据存储和历史查询
- 实时推送和webhook集成
- 群聊统计和分析报告
- 多设备同步和集群部署

## 🏆 项目价值

这是一个**完整、稳定、可投入生产使用**的微信群消息监控系统：

- ✅ **技术先进**：突破关键技术难点
- ✅ **功能完整**：覆盖所有核心需求  
- ✅ **架构清晰**：易于理解和扩展
- ✅ **文档完善**：便于维护和传承
- ✅ **测试充分**：经过完整验证

**为微信群聊数据采集提供了完整的解决方案！**
