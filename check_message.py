#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查特定时间戳的消息
"""

import requests
import json
import datetime

def check_message():
    host = "http://127.0.0.1:50007"
    pid = "22504"
    headers = {"X-WeChat-PID": str(pid)}
    
    # 获取数据库句柄
    response = requests.post(f"{host}/api/db/getDBInfo", headers=headers)
    data = response.json()
    
    db_handle = None
    for db in data["Data"]:
        if db.get("databaseName") == "MSG0.db":
            db_handle = db.get("handle")
            break
    
    print(f"数据库句柄: {db_handle}")
    
    # 检查最新的几条消息
    sql = """
    SELECT localId, TalkerId, CreateTime, StrTalker, StrContent, IsSender, Type
    FROM MSG 
    WHERE StrTalker LIKE '%@chatroom'
    ORDER BY CreateTime DESC 
    LIMIT 10;
    """
    
    data = {
        "dbHandle": str(db_handle),
        "sql": sql
    }
    
    response = requests.post(f"{host}/api/db/execSql", headers=headers, json=data)
    result = response.json()
    
    print("最新的10条群聊消息:")
    if result.get("Data"):
        for i, row in enumerate(result["Data"], 1):
            if len(row) >= 7 and str(row[2]).isdigit():
                local_id = row[0]
                create_time = int(row[2])
                str_talker = row[3]
                content = row[4]
                is_sender = row[5]
                
                # 格式化时间
                try:
                    send_time = datetime.datetime.fromtimestamp(create_time).strftime("%Y-%m-%d %H:%M:%S")
                except:
                    send_time = "未知时间"
                
                print(f"{i}. ID:{local_id} 时间:{create_time}({send_time}) 群:{str_talker} 内容:{content} 发送者:{is_sender}")
    
    # 检查特定时间戳之后的消息
    target_time = 1755578884  # 刚才发送的消息时间戳
    print(f"\n检查时间戳 {target_time} 之后的消息:")
    
    sql2 = f"""
    SELECT localId, TalkerId, CreateTime, StrTalker, StrContent, IsSender, Type
    FROM MSG 
    WHERE CreateTime >= {target_time}
    AND StrTalker LIKE '%@chatroom'
    ORDER BY CreateTime DESC;
    """
    
    data2 = {
        "dbHandle": str(db_handle),
        "sql": sql2
    }
    
    response2 = requests.post(f"{host}/api/db/execSql", headers=headers, json=data2)
    result2 = response2.json()
    
    if result2.get("Data"):
        print(f"找到 {len(result2['Data'])} 条消息:")
        for row in result2["Data"]:
            if len(row) >= 7 and str(row[2]).isdigit():
                local_id = row[0]
                create_time = int(row[2])
                str_talker = row[3]
                content = row[4]
                is_sender = row[5]
                
                try:
                    send_time = datetime.datetime.fromtimestamp(create_time).strftime("%Y-%m-%d %H:%M:%S")
                except:
                    send_time = "未知时间"
                
                print(f"ID:{local_id} 时间:{create_time}({send_time}) 群:{str_talker} 内容:{content}")
    else:
        print("没有找到消息")

if __name__ == "__main__":
    check_message()
