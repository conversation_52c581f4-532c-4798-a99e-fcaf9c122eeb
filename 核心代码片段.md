# 微信群消息监控 - 核心代码片段

## 给下一个AI的关键代码

### 1. 发送者昵称提取（最关键）

```python
import base64
import re

def extract_sender_wxid(bytes_extra):
    """
    从BytesExtra字段提取发送者微信ID
    这是获取真实昵称的核心技术
    """
    if not bytes_extra:
        return None
    
    try:
        bytes_str = str(bytes_extra)
        
        # 关键技术：base64解码
        try:
            decoded = base64.b64decode(bytes_str).decode('utf-8', errors='ignore')
            # 查找微信ID模式：wxid_后跟字母数字
            wxid_pattern = r'wxid_[a-zA-Z0-9]+'
            match = re.search(wxid_pattern, decoded)
            if match:
                return match.group(0)  # 返回如：wxid_0f9dl5advg622
        except:
            pass
        
        # 备用方案：直接在原字符串中查找
        wxid_pattern = r'wxid_[a-zA-Z0-9]+'
        match = re.search(wxid_pattern, bytes_str)
        if match:
            return match.group(0)
        
        return None
    except:
        return None
```

### 2. 数据库连接和句柄获取

```python
import requests

def get_db_handles():
    """获取微信数据库句柄"""
    host = "http://127.0.0.1:50007"
    pid = "22504"
    headers = {"X-WeChat-PID": str(pid)}
    
    response = requests.post(f"{host}/api/db/getDBInfo", headers=headers)
    data = response.json()
    
    msg_handle = None      # 消息数据库
    micromsg_handle = None # 联系人数据库
    
    for db in data["Data"]:
        if db.get("databaseName") == "MSG0.db":
            msg_handle = db.get("handle")
        elif "MicroMsg" in db.get("databaseName", ""):
            micromsg_handle = db.get("handle")
    
    return host, pid, msg_handle, micromsg_handle
```

### 3. 联系人信息加载

```python
def load_contacts(host, pid, micromsg_handle):
    """加载所有联系人信息到缓存"""
    headers = {"X-WeChat-PID": str(pid)}
    
    sql = "SELECT UserName, NickName, Remark FROM Contact;"
    data = {
        "dbHandle": str(micromsg_handle),
        "sql": sql
    }
    
    contact_cache = {}
    response = requests.post(f"{host}/api/db/execSql", headers=headers, json=data)
    result = response.json()
    
    if result.get("Data"):
        for row in result["Data"][1:]:  # 跳过列名行
            if len(row) >= 3:
                username = row[0]    # 微信ID
                nickname = row[1] if row[1] else username  # 昵称
                remark = row[2] if row[2] else ""          # 备注
                
                # 优先级：备注 > 昵称 > 微信ID
                display_name = remark if remark else nickname
                contact_cache[username] = display_name
    
    return contact_cache
```

### 4. 新消息查询

```python
def get_new_messages(host, pid, msg_handle, last_msg_id):
    """查询新的群聊消息"""
    headers = {"X-WeChat-PID": str(pid)}
    
    # 关键SQL：查询群聊新消息
    sql = f"""
    SELECT localId, TalkerId, CreateTime, StrTalker, StrContent, IsSender, Type, BytesExtra
    FROM MSG 
    WHERE localId > {last_msg_id}
    AND StrTalker LIKE '%@chatroom'
    ORDER BY localId ASC;
    """
    
    data = {
        "dbHandle": str(msg_handle),
        "sql": sql
    }
    
    messages = []
    response = requests.post(f"{host}/api/db/execSql", headers=headers, json=data)
    result = response.json()
    
    if result.get("Data"):
        for row in result["Data"][1:]:  # 跳过列名行
            if len(row) >= 8 and str(row[0]).isdigit():
                msg = {
                    "localId": int(row[0]),
                    "createTime": int(row[2]) if str(row[2]).isdigit() else 0,
                    "talker": row[3],      # 群ID
                    "content": row[4],     # 消息内容
                    "isSender": row[5],    # 是否自己发送
                    "type": row[6],        # 消息类型
                    "bytesExtra": row[7]   # 包含发送者信息的字段
                }
                messages.append(msg)
    
    return messages
```

### 5. 消息格式化

```python
import datetime

def format_message(msg, contact_cache):
    """格式化消息为JSON输出格式"""
    
    # 获取群名称
    group_id = msg.get("talker")
    group_name = contact_cache.get(group_id, group_id)
    
    # 格式化时间
    timestamp = int(msg.get("createTime", 0))
    try:
        send_time = datetime.datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S")
    except:
        send_time = "未知时间"
    
    # 获取发送者信息（核心逻辑）
    is_sender = str(msg.get("isSender", 0))
    if is_sender == "1":
        sender_name = "我"
    else:
        # 从BytesExtra中提取发送者微信ID
        bytes_extra = msg.get("bytesExtra")
        sender_wxid = extract_sender_wxid(bytes_extra)
        
        if sender_wxid:
            # 从联系人缓存中获取昵称
            sender_name = contact_cache.get(sender_wxid, sender_wxid)
        else:
            sender_name = "未知用户"
    
    return {
        "发送人": sender_name,
        "发送时间": send_time,
        "群聊名称": group_name,
        "消息内容": msg.get("content", ""),
        "消息类型": str(msg.get("type", 0)),
        "消息ID": str(msg.get("localId", ""))
    }
```

### 6. 主监控循环

```python
import time
import json

def monitor_messages():
    """主监控函数"""
    
    # 1. 初始化
    host, pid, msg_handle, micromsg_handle = get_db_handles()
    contact_cache = load_contacts(host, pid, micromsg_handle)
    
    # 2. 获取当前最新消息ID作为起点
    headers = {"X-WeChat-PID": str(pid)}
    sql_init = "SELECT MAX(localId) as maxId FROM MSG WHERE StrTalker LIKE '%@chatroom';"
    data_init = {"dbHandle": str(msg_handle), "sql": sql_init}
    
    response_init = requests.post(f"{host}/api/db/execSql", headers=headers, json=data_init)
    result_init = response_init.json()
    
    last_msg_id = 0
    if result_init.get("Data") and len(result_init["Data"]) > 1:
        for row in result_init["Data"][1:]:
            if row and len(row) > 0 and str(row[0]).isdigit():
                last_msg_id = int(row[0])
                break
    
    print(f"开始监控，当前最新消息ID: {last_msg_id}")
    
    # 3. 监控循环
    while True:
        try:
            # 获取新消息
            new_messages = get_new_messages(host, pid, msg_handle, last_msg_id)
            
            # 处理每条新消息
            for msg in new_messages:
                formatted_msg = format_message(msg, contact_cache)
                
                # 输出JSON格式
                print(json.dumps(formatted_msg, ensure_ascii=False, indent=2))
                print("-" * 30)
                
                # 更新最新消息ID
                last_msg_id = msg["localId"]
            
            # 等待3秒
            time.sleep(3)
            
        except KeyboardInterrupt:
            print("监控已停止")
            break
        except Exception as e:
            print(f"错误: {e}")
            time.sleep(5)
```

### 7. 完整最小实现

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信群消息监控 - 最小完整实现
包含所有核心功能的最简版本
"""

import requests
import json
import time
import datetime
import re
import base64

# 在这里插入上面的所有函数...

def main():
    print("微信群消息监控程序（带昵称）")
    print("=" * 50)
    
    try:
        monitor_messages()
    except Exception as e:
        print(f"程序错误: {e}")

if __name__ == "__main__":
    main()
```

## 关键技术点说明

### 1. BytesExtra字段解析
- **问题**：群聊消息的发送者信息不在明文字段中
- **解决**：BytesExtra字段包含base64编码的protobuf数据
- **方法**：base64解码后用正则表达式提取微信ID

### 2. 数据库表关系
- **MSG表**：存储消息，StrTalker字段标识群聊（以@chatroom结尾）
- **Contact表**：存储联系人，UserName对应微信ID，NickName是昵称

### 3. 实时监控策略
- **方法**：使用localId递增特性，只查询比last_msg_id大的消息
- **频率**：3秒轮询，平衡实时性和性能

### 4. 昵称显示优先级
1. 备注名（Remark）- 用户自定义的备注
2. 昵称（NickName）- 用户设置的昵称
3. 微信ID（UserName）- 原始微信ID

## 测试验证

运行程序后，在微信群中发送消息，应该看到类似输出：

```json
{
  "发送人": "小张",
  "发送时间": "2025-08-19 13:23:03",
  "群聊名称": "测试群",
  "消息内容": "大家好！",
  "消息类型": "1",
  "消息ID": "5815"
}
```

如果显示的是微信ID而不是昵称，说明BytesExtra解析有问题，需要检查extract_sender_wxid函数。
