# 微信群消息监控 + 飞书上传 + Coze自动回复 - 配置说明

## 🎯 唯一正式版本

**`wechat_feishu_coze_monitor.py`** - 这是唯一的正式版本，集成了所有功能：
- ✅ 微信群消息实时监控
- ✅ 飞书多维表格自动上传
- ✅ Coze AI自动回复
- ✅ 真实发送人昵称显示
- ✅ 引用消息识别

## ⚙️ 配置方法

### 方法1：使用配置助手（推荐）

#### 配置飞书
```bash
python 配置飞书密钥.py
```

#### 配置Coze
```bash
python 配置Coze.py
```

### 方法2：手动编辑
编辑 `wechat_feishu_coze_monitor.py` 文件中的 CONFIG 部分：

```python
CONFIG = {
    # 飞书配置
    "feishu": {
        "app_id": "cli_a828491ea031d013",
        "app_secret": "在此填入飞书应用密钥",  # 必填
        "app_token": "TtULb7pBiaGRMgs4dfac4aLAnId",
        "table_id": "",    # 程序会自动获取第一个表格ID
    },

    # Coze配置
    "coze": {
        "api_url": "https://api.coze.cn/open_api/v2/chat",  # Coze API地址
        "api_key": "在此填入Coze API密钥",     # 必填
        "bot_id": "在此填入Coze Bot ID"       # 必填
    }
}
```

## 📋 需要获取的信息

### 1. 飞书应用密钥（必需）
- 访问：https://open.feishu.cn/
- 找到应用：`cli_a828491ea031d013`
- 复制应用密钥

### 2. 飞书表格ID（自动获取）
- 程序会自动获取多维表格中的第一个表格
- 无需手动配置

### 3. Coze配置（AI自动回复）
- API地址：通常是 https://api.coze.cn/open_api/v2/chat
- API密钥：从Coze平台获取
- Bot ID：创建的机器人ID

## 🚀 使用方法

### 完整功能启动
```bash
python wechat_feishu_coze_monitor.py
```

### 部分功能使用
- **只配置飞书**：只上传到飞书，不自动回复
- **只配置Coze**：只AI自动回复，不上传到飞书
- **都配置**：完整功能，监控+上传+AI回复
- **都不配置**：只监控显示消息

## 📊 输出格式

程序会输出完全匹配您飞书多维表格的字段格式：

```json
{
  "发送时间": "2025-08-20 15:30:00",
  "发送人": "小张",
  "群聊名称": "测试群",
  "消息内容": "测试消息",
  "消息ID": "1234567890123456789",
  "消息附件": "",
  "消息类型": "1",
  "被引用消息ID": ""
}
```

## 🔧 功能说明

### 自动回复规则
- 不回复自己的消息
- 只回复文本消息（类型=1）
- 消息内容长度大于2个字符
- 通过Coze AI生成回复内容

### 飞书上传
- 自动上传所有群消息到多维表格
- 字段完全匹配您的表格结构
- 包含发送人、时间、内容、引用关系等

### 消息监控
- 实时监控所有群聊消息
- 显示真实发送人昵称
- 识别引用回复关系
- 统一使用MsgSvrID保证唯一性

## ⚠️ 注意事项

1. **微信客户端**：必须保持运行状态
2. **RPC服务**：确保微信RPC服务正常运行
3. **网络连接**：确保能访问飞书和Coze API
4. **权限配置**：确保飞书应用有多维表格读写权限

## 🎉 使用效果

启动后程序会：
1. 实时监控微信群消息
2. 在控制台显示格式化的消息信息
3. 自动上传到飞书多维表格
4. 通过Coze AI自动回复相关消息

**这是唯一的正式版本，集成了所有功能，可以直接投入使用！**
