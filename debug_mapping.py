#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试TalkerId映射
"""

import requests
import json

def debug_mapping():
    host = "http://127.0.0.1:50007"
    pid = "22504"
    headers = {"X-WeChat-PID": pid}
    
    # 获取数据库句柄
    url = f"{host}/api/db/getDBInfo"
    response = requests.post(url, headers=headers)
    data = response.json()
    
    db_handle = None
    for db in data["Data"]:
        if db.get("databaseName") == "MSG0.db":
            db_handle = db.get("handle")
            break
    
    print(f"数据库句柄: {db_handle}")
    
    # 查询Name2ID表
    url2 = f"{host}/api/db/execSql"
    sql = "SELECT * FROM Name2ID LIMIT 20;"
    data2 = {
        "dbHandle": str(db_handle),
        "sql": sql
    }
    
    response2 = requests.post(url2, headers=headers, json=data2)
    result = response2.json()
    
    print("Name2ID表内容:")
    print(json.dumps(result, ensure_ascii=False, indent=2))
    
    # 查询MSGTrans表
    sql3 = "SELECT * FROM MSGTrans LIMIT 20;"
    data3 = {
        "dbHandle": str(db_handle),
        "sql": sql3
    }
    
    response3 = requests.post(url2, headers=headers, json=data3)
    result3 = response3.json()
    
    print("\nMSGTrans表内容:")
    print(json.dumps(result3, ensure_ascii=False, indent=2))
    
    # 查询最新消息的TalkerId
    sql4 = "SELECT DISTINCT TalkerId, StrTalker FROM MSG ORDER BY CreateTime DESC LIMIT 20;"
    data4 = {
        "dbHandle": str(db_handle),
        "sql": sql4
    }
    
    response4 = requests.post(url2, headers=headers, json=data4)
    result4 = response4.json()
    
    print("\n消息中的TalkerId和StrTalker:")
    print(json.dumps(result4, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    debug_mapping()
