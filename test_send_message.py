#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试发送消息到群聊
"""

import requests
import time

def send_test_message():
    host = "http://127.0.0.1:50007"
    pid = "22504"
    headers = {"X-WeChat-PID": pid}
    
    # 发送测试消息到测试群
    test_group_id = "56347303221@chatroom"  # 测试群
    test_message = f"测试消息 - {time.strftime('%H:%M:%S')}"
    
    url = f"{host}/api/msg/sendTextMsg"
    data = {
        "wxid": test_group_id,
        "msg": test_message
    }
    
    try:
        response = requests.post(url, headers=headers, json=data)
        print(f"发送消息状态码: {response.status_code}")
        print(f"发送消息响应: {response.text}")
        print(f"发送的消息: {test_message}")
        print(f"发送到群: {test_group_id}")
    except Exception as e:
        print(f"发送消息失败: {e}")

if __name__ == "__main__":
    send_test_message()
