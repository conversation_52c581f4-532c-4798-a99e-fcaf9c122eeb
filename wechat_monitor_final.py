#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信群消息监控程序 - 正式版
功能：实时监控微信群消息，显示发送人真实昵称和引用信息
特点：统一使用MsgSvrID作为消息ID，保证全局唯一性
"""

import requests
import json
import time
import datetime
import re
import base64

def extract_sender_wxid(bytes_extra):
    """从BytesExtra中提取发送者微信ID"""
    if not bytes_extra:
        return None
    
    try:
        bytes_str = str(bytes_extra)
        
        # 尝试base64解码
        try:
            decoded = base64.b64decode(bytes_str).decode('utf-8', errors='ignore')
            wxid_pattern = r'wxid_[a-zA-Z0-9]+'
            match = re.search(wxid_pattern, decoded)
            if match:
                return match.group(0)
        except:
            pass
        
        # 直接查找
        wxid_pattern = r'wxid_[a-zA-Z0-9]+'
        match = re.search(wxid_pattern, bytes_str)
        if match:
            return match.group(0)
        
        return None
    except:
        return None

def is_quote_message(msg):
    """判断是否为引用消息"""
    return (
        str(msg.get("type")) == "49" and 
        str(msg.get("subType")) in ["51", "57"] and 
        msg.get("compressContent") is not None
    )

def parse_quote_info(compress_content):
    """解析引用消息信息 - 只提取MsgSvrID"""
    if not compress_content:
        return None
    
    try:
        # base64解码
        decoded = base64.b64decode(compress_content).decode('utf-8', errors='ignore')
        
        # 只提取被引用消息的服务器ID (MsgSvrID)
        svrid_matches = re.findall(r'\b(\d{15,20})\b', decoded)
        if svrid_matches:
            return {
                "被引用消息ID": svrid_matches[0]
            }
        
        return None
        
    except Exception as e:
        return None

def main():
    print("=" * 70)
    print("微信群消息监控程序 - 正式版")
    print("功能：实时监控微信群消息，显示发送人真实昵称和引用信息")
    print("特点：统一使用MsgSvrID作为消息ID，保证全局唯一性")
    print("=" * 70)
    
    host = "http://127.0.0.1:50007"
    pid = "22504"
    headers = {"X-WeChat-PID": str(pid)}
    
    try:
        # 获取数据库句柄
        print("正在连接数据库...")
        response = requests.post(f"{host}/api/db/getDBInfo", headers=headers, timeout=10)
        data = response.json()
        
        msg_handle = None
        micromsg_handle = None
        
        for db in data["Data"]:
            if db.get("databaseName") == "MSG0.db":
                msg_handle = db.get("handle")
            elif "MicroMsg" in db.get("databaseName", ""):
                micromsg_handle = db.get("handle")
        
        if not all([msg_handle, micromsg_handle]):
            print("❌ 数据库连接失败")
            return
        
        print(f"✅ 数据库连接成功")
        
        # 加载联系人信息（优化版本，只加载群聊和常用联系人）
        print("正在加载联系人信息...")
        sql_contact = """
        SELECT UserName, NickName, Remark FROM Contact
        WHERE UserName LIKE '%@chatroom' OR UserName LIKE 'wxid_%'
        LIMIT 200;
        """
        data_contact = {"dbHandle": str(micromsg_handle), "sql": sql_contact}

        contact_cache = {}
        try:
            response_contact = requests.post(f"{host}/api/db/execSql", headers=headers, json=data_contact, timeout=15)
            result_contact = response_contact.json()

            if result_contact.get("Data"):
                for row in result_contact["Data"][1:]:  # 跳过列名
                    if len(row) >= 3:
                        username = row[0]
                        nickname = row[1] if row[1] else username
                        remark = row[2] if row[2] else ""
                        display_name = remark if remark else nickname
                        contact_cache[username] = display_name

            print(f"✅ 加载了 {len(contact_cache)} 个联系人信息")
        except Exception as e:
            print(f"⚠️  联系人加载失败，使用基础缓存: {e}")
            # 使用基础联系人缓存
            contact_cache = {
                "wxid_44ryxbf3scuq22": "小张",
                "wxid_0f9dl5advg6222": "Wonder",
                "wxid_i18pfbai664r22": "我来收集总结你们的聊天(bot)",
                "56347303221@chatroom": "测试群",
                "46113402449@chatroom": "业务AI化小组"
            }
        
        # 获取当前最新消息ID作为起始点
        print("正在初始化...")
        sql_init = "SELECT MAX(localId) as maxId FROM MSG WHERE StrTalker LIKE '%@chatroom';"
        data_init = {"dbHandle": str(msg_handle), "sql": sql_init}
        
        response_init = requests.post(f"{host}/api/db/execSql", headers=headers, json=data_init, timeout=10)
        result_init = response_init.json()
        
        last_msg_id = 0
        if result_init.get("Data") and len(result_init["Data"]) > 1:
            for row in result_init["Data"][1:]:
                if row and len(row) > 0 and str(row[0]).isdigit():
                    last_msg_id = int(row[0])
                    break
        
        print(f"✅ 初始化完成，最新消息ID: {last_msg_id}")
        print(f"\n🚀 开始实时监控（每3秒检查一次）...")
        print("=" * 70)
        
        # 开始监控循环
        while True:
            try:
                # 查询新消息（包含MsgSvrID字段）
                sql_new = f"""
                SELECT localId, TalkerId, CreateTime, StrTalker, StrContent, IsSender, Type, SubType, BytesExtra, CompressContent, MsgSvrID
                FROM MSG 
                WHERE localId > {last_msg_id}
                AND StrTalker LIKE '%@chatroom'
                ORDER BY localId ASC;
                """
                
                data_new = {"dbHandle": str(msg_handle), "sql": sql_new}
                response_new = requests.post(f"{host}/api/db/execSql", headers=headers, json=data_new, timeout=10)
                result_new = response_new.json()
                
                if result_new.get("Data") and len(result_new["Data"]) > 1:
                    for row in result_new["Data"][1:]:  # 跳过列名
                        if len(row) >= 11 and str(row[0]).isdigit():
                            local_id = int(row[0])
                            create_time = int(row[2]) if str(row[2]).isdigit() else 0
                            group_id = row[3]
                            content = row[4]
                            is_sender = row[5]
                            msg_type = row[6]
                            sub_type = row[7]
                            bytes_extra = row[8]
                            compress_content = row[9]
                            msg_svr_id = row[10]  # MsgSvrID
                            
                            # 构建消息对象
                            msg = {
                                "localId": local_id,
                                "createTime": create_time,
                                "talker": group_id,
                                "content": content,
                                "isSender": is_sender,
                                "type": msg_type,
                                "subType": sub_type,
                                "bytesExtra": bytes_extra,
                                "compressContent": compress_content,
                                "msgSvrId": msg_svr_id
                            }
                            
                            # 格式化时间
                            try:
                                send_time = datetime.datetime.fromtimestamp(create_time).strftime("%Y-%m-%d %H:%M:%S")
                            except:
                                send_time = "未知时间"
                            
                            # 获取群名称
                            group_name = contact_cache.get(group_id, group_id)
                            
                            # 获取发送者信息
                            if str(is_sender) == "1":
                                sender_name = "我"
                            else:
                                sender_wxid = extract_sender_wxid(bytes_extra)
                                if sender_wxid:
                                    sender_name = contact_cache.get(sender_wxid, sender_wxid)
                                else:
                                    sender_name = "未知用户"
                            
                            # 检查是否为引用消息
                            quote_info = None
                            if is_quote_message(msg):
                                quote_info = parse_quote_info(compress_content)
                            
                            # 构建并输出消息（统一使用MsgSvrID）
                            formatted_msg = {
                                "发送人": sender_name,
                                "发送时间": send_time,
                                "群聊名称": group_name,
                                "消息内容": content,
                                "消息类型": str(msg_type),
                                "消息ID": str(msg_svr_id) if msg_svr_id and str(msg_svr_id) != "0" else f"local_{local_id}",
                                "引用信息": quote_info if quote_info else {}
                            }
                            
                            print(json.dumps(formatted_msg, ensure_ascii=False, indent=2))
                            print("-" * 30)
                            
                            # 更新最新消息ID
                            last_msg_id = local_id
                
                # 等待3秒
                time.sleep(3)
                
            except KeyboardInterrupt:
                print("\n⏹️  监控已停止")
                break
            except Exception as e:
                print(f"❌ 处理消息时出错: {e}")
                time.sleep(5)
                
    except Exception as e:
        print(f"❌ 程序初始化错误: {e}")

if __name__ == "__main__":
    main()
