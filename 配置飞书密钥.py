#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置飞书应用密钥
"""

import re

def update_config():
    print("=" * 60)
    print("配置飞书应用密钥")
    print("=" * 60)
    
    print("您的飞书应用信息：")
    print("- 应用ID: cli_a828491ea031d013")
    print("- 多维表格链接: https://gwvwal7cgy.feishu.cn/base/TtULb7pBiaGRMgs4dfac4aLAnId")
    
    print("\n📝 获取应用密钥步骤：")
    print("1. 访问：https://open.feishu.cn/")
    print("2. 找到应用：cli_a828491ea031d013")
    print("3. 复制应用密钥 (App Secret)")
    
    app_secret = input("\n请输入飞书应用密钥: ").strip()
    
    if not app_secret:
        print("❌ 应用密钥不能为空")
        return
    
    # 验证格式
    if not app_secret.startswith("cli_a828491ea031d013-"):
        print("⚠️  应用密钥格式可能不正确，但仍会保存")
    
    # 读取当前文件
    try:
        with open("wechat_feishu_coze_monitor.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 替换应用密钥
        pattern = r'"app_secret": "",  # 需要从飞书开放平台获取应用密钥'
        replacement = f'"app_secret": "{app_secret}",  # 飞书应用密钥'
        
        new_content = re.sub(pattern, replacement, content)
        
        if new_content != content:
            # 写回文件
            with open("wechat_feishu_coze_monitor.py", "w", encoding="utf-8") as f:
                f.write(new_content)
            
            print("✅ 应用密钥配置成功！")
            print("\n🚀 现在可以运行完整程序：")
            print("python wechat_feishu_coze_monitor.py")
        else:
            print("❌ 配置失败，未找到配置位置")
    
    except Exception as e:
        print(f"❌ 配置失败: {e}")

if __name__ == "__main__":
    update_config()
