#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终版微信群消息监控程序
"""

import requests
import json
import time
import datetime

def get_wechat_info():
    """获取微信信息"""
    host = "http://127.0.0.1:50007"
    
    # 获取微信PID
    response = requests.get(f"{host}/api/wechat/list")
    data = response.json()
    if not data.get("Data"):
        return None, None, None
    
    pid = data["Data"][0].get("Pid")
    headers = {"X-WeChat-PID": str(pid)}
    
    # 获取数据库句柄
    response = requests.post(f"{host}/api/db/getDBInfo", headers=headers)
    data = response.json()
    db_handle = None
    for db in data["Data"]:
        if db.get("databaseName") == "MSG0.db":
            db_handle = db.get("handle")
            break
    
    return host, pid, db_handle

def get_groups(host, pid):
    """获取群聊列表"""
    headers = {"X-WeChat-PID": str(pid)}
    response = requests.post(f"{host}/api/session/getSessionList", headers=headers)
    data = response.json()
    
    if data.get("Data"):
        groups = [s for s in data["Data"] if s.get("wxid", "").endswith("@chatroom")]
        return groups
    return []

def get_new_messages(host, pid, db_handle, since_time):
    """获取新消息"""
    headers = {"X-WeChat-PID": str(pid)}
    
    sql = f"""
    SELECT localId, TalkerId, CreateTime, StrTalker, StrContent, IsSender, Type
    FROM MSG 
    WHERE CreateTime > {since_time} 
    AND StrTalker LIKE '%@chatroom'
    ORDER BY CreateTime ASC;
    """
    
    data = {
        "dbHandle": str(db_handle),
        "sql": sql
    }
    
    response = requests.post(f"{host}/api/db/execSql", headers=headers, json=data)
    result = response.json()
    
    messages = []
    if result.get("Data"):
        for row in result["Data"]:
            if len(row) >= 7:
                messages.append({
                    "localId": row[0],
                    "talkerId": row[1], 
                    "createTime": row[2],
                    "talker": row[3],
                    "content": row[4],
                    "isSender": row[5],
                    "type": row[6]
                })
    return messages

def format_message(msg, groups):
    """格式化消息"""
    # 获取群名称
    group_id = msg.get("talker")
    group_name = group_id
    for group in groups:
        if group.get("wxid") == group_id:
            group_name = group.get("nickname", group_id)
            break
    
    # 格式化时间
    timestamp = int(msg.get("createTime", 0))
    try:
        send_time = datetime.datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S")
    except:
        send_time = "未知时间"
    
    # 发送人
    is_sender = str(msg.get("isSender", 0))
    sender = "我" if is_sender == "1" else msg.get("talker", "未知")
    
    return {
        "发送人": sender,
        "发送时间": send_time,
        "群聊名称": group_name,
        "消息内容": msg.get("content", ""),
        "消息类型": str(msg.get("type", 0)),
        "消息ID": str(msg.get("localId", ""))
    }

def main():
    print("=" * 60)
    print("微信群消息监控程序")
    print("=" * 60)
    
    try:
        # 初始化
        print("正在初始化...")
        host, pid, db_handle = get_wechat_info()
        
        if not all([host, pid, db_handle]):
            print("❌ 初始化失败")
            return
        
        print(f"✅ 微信PID: {pid}")
        print(f"✅ 数据库句柄: {db_handle}")
        
        # 获取群聊
        groups = get_groups(host, pid)
        print(f"✅ 找到 {len(groups)} 个群聊")
        
        # 显示群聊列表
        print("\n监控的群聊:")
        for i, group in enumerate(groups[:5], 1):
            print(f"  {i}. {group.get('nickname', group.get('wxid'))}")
        if len(groups) > 5:
            print(f"  ... 还有 {len(groups) - 5} 个群聊")
        
        print(f"\n🚀 开始实时监控（每3秒检查一次）...")
        print("=" * 60)
        
        # 开始监控
        last_check_time = int(time.time())
        
        while True:
            try:
                # 获取新消息
                new_messages = get_new_messages(host, pid, db_handle, last_check_time)
                
                for msg in new_messages:
                    # 格式化并输出
                    formatted_msg = format_message(msg, groups)
                    print(json.dumps(formatted_msg, ensure_ascii=False, indent=2))
                    print("-" * 30)
                    
                    # 更新检查时间
                    msg_time = int(msg.get("createTime", 0))
                    if msg_time > last_check_time:
                        last_check_time = msg_time
                
                # 等待3秒
                time.sleep(3)
                
            except KeyboardInterrupt:
                print("\n⏹️  监控已停止")
                break
            except Exception as e:
                print(f"❌ 检查消息时出错: {e}")
                time.sleep(5)
                
    except Exception as e:
        print(f"❌ 程序错误: {e}")

if __name__ == "__main__":
    main()
