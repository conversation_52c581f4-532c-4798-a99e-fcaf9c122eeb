#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信群消息监控脚本
功能：监控获取全部群的新消息，输出发送人、发送时间、群聊名称、消息内容
"""

import requests
import json
import time
import datetime
from typing import Dict, List, Optional

class WeChatMonitor:
    def __init__(self, host: str = "http://127.0.0.1:50007"):
        """
        初始化微信监控器
        
        Args:
            host: 微信RPC服务地址
        """
        self.host = host
        self.wechat_pid = None
        self.session = requests.Session()
        self.last_msg_ids = {}  # 记录每个群的最后一条消息ID，用于检测新消息
        
    def get_wechat_clients(self) -> List[Dict]:
        """获取微信客户端列表"""
        try:
            url = f"{self.host}/api/wechat/list"
            response = self.session.get(url)
            response.raise_for_status()
            
            data = response.json()
            if data.get("Code") == 1 and data.get("Data"):
                return data["Data"]
            else:
                print(f"获取微信客户端列表失败: {data}")
                return []
        except Exception as e:
            print(f"获取微信客户端列表异常: {e}")
            return []
    
    def set_wechat_pid(self, pid: str = None):
        """设置微信进程ID"""
        if pid:
            self.wechat_pid = pid
        else:
            # 自动获取第一个微信客户端的PID
            clients = self.get_wechat_clients()
            if clients:
                self.wechat_pid = clients[0].get("Pid")
                print(f"自动选择微信客户端 PID: {self.wechat_pid}")
            else:
                raise Exception("未找到可用的微信客户端")
    
    def get_session_list(self) -> List[Dict]:
        """获取会话列表"""
        try:
            url = f"{self.host}/api/session/getSessionList"
            headers = {"X-WeChat-PID": str(self.wechat_pid)}

            response = self.session.post(url, headers=headers)
            response.raise_for_status()

            data = response.json()
            # 修复：有些接口返回Code为-1但仍然成功，检查是否有Data
            if data.get("Data"):
                return data["Data"]
            else:
                print(f"获取会话列表失败: {data}")
                return []
        except Exception as e:
            print(f"获取会话列表异常: {e}")
            return []
    
    def get_group_sessions(self) -> List[Dict]:
        """获取群聊会话列表"""
        sessions = self.get_session_list()
        # 过滤出群聊（群聊ID通常以@chatroom结尾）
        group_sessions = [s for s in sessions if s.get("wxid", "").endswith("@chatroom")]
        return group_sessions
    
    def get_db_handle(self):
        """获取消息数据库句柄"""
        try:
            url = f"{self.host}/api/db/getDBInfo"
            headers = {"X-WeChat-PID": str(self.wechat_pid)}

            response = self.session.post(url, headers=headers)
            response.raise_for_status()

            data = response.json()
            if data.get("Data"):
                db_info = data["Data"]
                # 查找MSG0.db数据库
                for db in db_info:
                    if db.get("databaseName") == "MSG0.db":
                        return db.get("handle")

                # 如果没找到MSG0.db，查找其他消息数据库
                for db in db_info:
                    if "MSG" in db.get("databaseName", "").upper():
                        return db.get("handle")
            return None
        except Exception as e:
            print(f"获取数据库句柄异常: {e}")
            return None

    def get_talker_id_mapping(self, db_handle):
        """获取TalkerId到wxid的映射"""
        try:
            url = f"{self.host}/api/db/execSql"
            headers = {"X-WeChat-PID": str(self.wechat_pid)}

            # 从MSG表中获取TalkerId和StrTalker的映射关系
            sql = "SELECT DISTINCT TalkerId, StrTalker FROM MSG WHERE StrTalker LIKE '%@chatroom';"
            data = {
                "dbHandle": str(db_handle),
                "sql": sql
            }

            response = self.session.post(url, headers=headers, json=data)
            response.raise_for_status()

            result = response.json()
            if result.get("Data"):
                # 构建映射字典 {wxid: talker_id}
                mapping = {}
                for row in result["Data"]:
                    if len(row) >= 2:
                        talker_id = int(row[0]) if str(row[0]).isdigit() else row[0]
                        wxid = row[1]
                        if wxid and wxid.endswith("@chatroom"):
                            mapping[wxid] = talker_id
                return mapping
            return {}
        except Exception as e:
            print(f"获取TalkerId映射异常: {e}")
            return {}

    def get_messages_by_db(self, db_handle: int, talker_ids: List[int], limit: int = 10) -> List[Dict]:
        """通过数据库查询获取消息"""
        try:
            url = f"{self.host}/api/db/execSql"
            headers = {"X-WeChat-PID": str(self.wechat_pid)}

            # 构建SQL查询，获取指定TalkerId的最新消息
            talker_ids_str = ",".join(map(str, talker_ids))
            sql = f"""
            SELECT localId, TalkerId, CreateTime, StrTalker, StrContent, IsSender, Type
            FROM MSG
            WHERE TalkerId IN ({talker_ids_str})
            ORDER BY CreateTime DESC
            LIMIT {limit};
            """

            data = {
                "dbHandle": str(db_handle),
                "sql": sql
            }

            response = self.session.post(url, headers=headers, json=data)
            response.raise_for_status()

            result = response.json()
            if result.get("Data"):
                messages = []
                for row in result["Data"]:
                    if len(row) >= 7:
                        msg = {
                            "localId": row[0],
                            "talkerId": row[1],
                            "createTime": row[2],
                            "talker": row[3],
                            "content": row[4],
                            "isSender": row[5],
                            "type": row[6]
                        }
                        messages.append(msg)
                return messages
            return []
        except Exception as e:
            print(f"数据库查询消息异常: {e}")
            return []
    
    def format_message_from_db(self, msg: Dict, group_name: str) -> Dict:
        """格式化从数据库获取的消息为JSON输出格式"""
        # 时间戳转换为可读时间
        timestamp = msg.get("createTime", 0)
        try:
            if timestamp and str(timestamp).isdigit():
                timestamp = int(timestamp)
                send_time = datetime.datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S")
            else:
                send_time = "未知时间"
        except (ValueError, OSError):
            send_time = "未知时间"

        # 获取发送人信息
        talker = msg.get("talker", "未知")
        is_sender = msg.get("isSender", 0)

        # 如果是自己发送的消息，显示为"我"
        if is_sender == 1:
            sender = "我"
        else:
            # 如果是群聊消息，talker字段包含群聊ID，需要进一步解析发送人
            sender = talker if talker != group_name else "未知用户"

        return {
            "发送人": sender,
            "发送时间": send_time,
            "群聊名称": group_name,
            "消息内容": msg.get("content", ""),
            "消息类型": msg.get("type", 0),
            "消息ID": msg.get("localId", "")
        }
    
    def check_new_messages(self):
        """检查所有群的新消息"""
        if not hasattr(self, 'db_handle') or not self.db_handle:
            return []

        group_sessions = self.get_group_sessions()
        if not group_sessions:
            return []

        new_messages = []

        # 获取群聊的TalkerId列表
        group_talker_ids = []
        group_id_to_name = {}

        for group in group_sessions:
            group_id = group.get("wxid")
            group_name = group.get("nickname", group_id)

            if not group_id:
                continue

            # 从映射中获取TalkerId
            talker_id = self.talker_mapping.get(group_id)
            if talker_id:
                group_talker_ids.append(talker_id)
                group_id_to_name[talker_id] = {
                    "wxid": group_id,
                    "name": group_name
                }

        if not group_talker_ids:
            return []

        # 获取最新的消息
        messages = self.get_messages_by_db(self.db_handle, group_talker_ids, limit=50)

        for msg in reversed(messages):  # 按时间正序处理
            talker_id = msg.get("talkerId")
            local_id = msg.get("localId")
            group_info = group_id_to_name.get(talker_id)

            if not group_info:
                continue

            group_id = group_info["wxid"]
            group_name = group_info["name"]

            # 检查是否有新消息
            last_known_msg_id = self.last_msg_ids.get(group_id)

            if last_known_msg_id is None:
                # 第一次运行，记录当前最新消息ID，不输出历史消息
                self.last_msg_ids[group_id] = local_id
                break
            elif local_id and local_id > last_known_msg_id:
                # 发现新消息
                formatted_msg = self.format_message_from_db(msg, group_name)
                new_messages.append(formatted_msg)
                self.last_msg_ids[group_id] = local_id

        return new_messages
    
    def start_monitoring(self, interval: int = 5):
        """开始监控"""
        print("正在初始化微信监控...")

        # 设置微信PID
        self.set_wechat_pid()

        if not self.wechat_pid:
            print("错误：无法获取微信客户端PID")
            return

        print(f"使用微信客户端 PID: {self.wechat_pid}")

        # 获取数据库句柄
        print("正在获取数据库连接...")
        self.db_handle = self.get_db_handle()
        if not self.db_handle:
            print("错误：无法获取数据库句柄")
            return

        print(f"数据库句柄: {self.db_handle}")

        # 获取TalkerId映射
        print("正在获取用户映射...")
        self.talker_mapping = self.get_talker_id_mapping(self.db_handle)
        print(f"获取到 {len(self.talker_mapping)} 个用户映射")

        # 获取群聊列表
        groups = self.get_group_sessions()
        print(f"找到 {len(groups)} 个群聊")

        # 初始化，记录当前最新消息ID
        print("正在初始化消息状态...")

        # 获取群聊的TalkerId列表
        group_talker_ids = []
        for group in groups:
            group_id = group.get("wxid")
            if group_id and group_id in self.talker_mapping:
                talker_id = self.talker_mapping[group_id]
                group_talker_ids.append(talker_id)

        if group_talker_ids:
            # 获取每个群的最新消息ID
            messages = self.get_messages_by_db(self.db_handle, group_talker_ids, limit=len(groups))
            for msg in messages:
                talker_id = msg.get("talkerId")
                local_id = msg.get("localId")

                # 找到对应的群ID
                for wxid, tid in self.talker_mapping.items():
                    if tid == talker_id and wxid.endswith("@chatroom"):
                        self.last_msg_ids[wxid] = local_id
                        break

        print(f"开始监控微信群消息，检查间隔: {interval}秒")
        print("=" * 50)

        try:
            while True:
                new_messages = self.check_new_messages()

                for msg in new_messages:
                    # 输出JSON格式的消息
                    print(json.dumps(msg, ensure_ascii=False, indent=2))
                    print("-" * 30)

                time.sleep(interval)

        except KeyboardInterrupt:
            print("\n监控已停止")
        except Exception as e:
            print(f"监控过程中发生错误: {e}")

def main():
    """主函数"""
    monitor = WeChatMonitor()
    monitor.start_monitoring(interval=3)  # 每3秒检查一次新消息

if __name__ == "__main__":
    main()
