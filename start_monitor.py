#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信群消息监控启动脚本
"""

from wechat_monitor import WeChatMonitor
import sys

def main():
    print("=" * 60)
    print("微信群消息监控程序")
    print("功能：实时监控所有群聊的新消息")
    print("输出格式：JSON（包含发送人、发送时间、群聊名称、消息内容）")
    print("=" * 60)
    
    try:
        # 创建监控器实例
        monitor = WeChatMonitor()
        
        # 开始监控（每3秒检查一次）
        monitor.start_monitoring(interval=3)
        
    except KeyboardInterrupt:
        print("\n程序已停止")
        sys.exit(0)
    except Exception as e:
        print(f"程序运行错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
