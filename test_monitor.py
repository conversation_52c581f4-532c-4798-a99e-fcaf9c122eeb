#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试微信监控功能
"""

from wechat_monitor import WeChatMonitor
import json

def test_monitor():
    """测试监控功能"""
    print("=== 测试微信监控功能 ===")
    
    monitor = WeChatMonitor()
    
    # 初始化
    monitor.set_wechat_pid()
    if not monitor.wechat_pid:
        print("错误：无法获取微信客户端PID")
        return
    
    print(f"微信PID: {monitor.wechat_pid}")
    
    # 获取数据库句柄
    monitor.db_handle = monitor.get_db_handle()
    if not monitor.db_handle:
        print("错误：无法获取数据库句柄")
        return
    
    print(f"数据库句柄: {monitor.db_handle}")
    
    # 获取映射
    monitor.talker_mapping = monitor.get_talker_id_mapping(monitor.db_handle)
    print(f"用户映射数量: {len(monitor.talker_mapping)}")
    
    # 获取群聊
    groups = monitor.get_group_sessions()
    print(f"群聊数量: {len(groups)}")
    
    # 显示前几个群聊
    print("\n群聊列表:")
    for i, group in enumerate(groups[:5]):
        group_id = group.get("wxid")
        group_name = group.get("nickname", group_id)
        talker_id = monitor.talker_mapping.get(group_id, "未找到")
        print(f"  {i+1}. {group_name} ({group_id}) - TalkerId: {talker_id}")
    
    # 获取群聊的TalkerId列表
    group_talker_ids = []
    group_info = {}
    
    for group in groups:
        group_id = group.get("wxid")
        group_name = group.get("nickname", group_id)
        
        if group_id in monitor.talker_mapping:
            talker_id = monitor.talker_mapping[group_id]
            group_talker_ids.append(talker_id)
            group_info[talker_id] = {
                "wxid": group_id,
                "name": group_name
            }
    
    print(f"\n有效群聊TalkerId: {group_talker_ids}")
    
    if group_talker_ids:
        # 获取最新消息
        print("\n=== 获取最新消息 ===")
        messages = monitor.get_messages_by_db(monitor.db_handle, group_talker_ids, limit=10)
        
        print(f"获取到 {len(messages)} 条消息")
        
        # 格式化并显示消息
        print("\n原始消息数据:")
        for i, msg in enumerate(messages[-3:]):  # 显示最新的3条
            print(f"消息 {i+1}: {msg}")

        print("\n格式化后的消息:")
        for msg in messages[-3:]:  # 显示最新的3条
            talker_id = msg.get("talkerId")
            print(f"处理消息，TalkerId: {talker_id}, 类型: {type(talker_id)}")

            # 确保talker_id是整数
            if isinstance(talker_id, str) and talker_id.isdigit():
                talker_id = int(talker_id)

            if talker_id in group_info:
                group_name = group_info[talker_id]["name"]
                print(f"找到群聊: {group_name}")
                formatted_msg = monitor.format_message_from_db(msg, group_name)
                print("\n消息:")
                print(json.dumps(formatted_msg, ensure_ascii=False, indent=2))
                print("-" * 30)
            else:
                print(f"未找到TalkerId {talker_id} 对应的群聊")

if __name__ == "__main__":
    test_monitor()
