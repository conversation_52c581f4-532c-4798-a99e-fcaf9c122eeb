# 微信群消息监控程序 - 快速实现指南

## 给下一个AI的实现指南

### 核心需求
监控微信群消息，输出：发送人昵称、发送时间、群聊名称、消息内容（JSON格式）

### 关键技术难点及解决方案

#### 1. 发送者昵称获取（最重要）
**问题**：群聊消息的发送者不是直接存储的，需要从BytesExtra字段解析

**解决方案**：
```python
import base64
import re

def extract_sender_wxid(bytes_extra):
    if not bytes_extra:
        return None
    
    try:
        bytes_str = str(bytes_extra)
        
        # 关键：base64解码
        try:
            decoded = base64.b64decode(bytes_str).decode('utf-8', errors='ignore')
            wxid_pattern = r'wxid_[a-zA-Z0-9]+'
            match = re.search(wxid_pattern, decoded)
            if match:
                return match.group(0)  # 返回类似 wxid_0f9dl5advg622
        except:
            pass
        
        # 备用方案：直接查找
        wxid_pattern = r'wxid_[a-zA-Z0-9]+'
        match = re.search(wxid_pattern, bytes_str)
        if match:
            return match.group(0)
        
        return None
    except:
        return None
```

#### 2. 数据库访问
**连接信息**：
- RPC服务：`http://127.0.0.1:50007`
- 微信PID：`22504`
- 消息数据库：`MSG0.db`
- 联系人数据库：`MicroMsg.db`

**关键SQL**：
```sql
-- 获取新消息（核心查询）
SELECT localId, TalkerId, CreateTime, StrTalker, StrContent, IsSender, Type, BytesExtra
FROM MSG 
WHERE localId > {last_msg_id}
AND StrTalker LIKE '%@chatroom'
ORDER BY localId ASC;

-- 获取联系人信息
SELECT UserName, NickName, Remark FROM Contact;
```

#### 3. 完整实现模板

```python
import requests
import json
import time
import datetime
import re
import base64

def main():
    # 1. 连接设置
    host = "http://127.0.0.1:50007"
    pid = "22504"
    headers = {"X-WeChat-PID": str(pid)}
    
    # 2. 获取数据库句柄
    response = requests.post(f"{host}/api/db/getDBInfo", headers=headers)
    data = response.json()
    
    msg_handle = None
    micromsg_handle = None
    
    for db in data["Data"]:
        if db.get("databaseName") == "MSG0.db":
            msg_handle = db.get("handle")
        elif "MicroMsg" in db.get("databaseName", ""):
            micromsg_handle = db.get("handle")
    
    # 3. 加载联系人信息（重要：缓存所有联系人）
    sql_contact = "SELECT UserName, NickName, Remark FROM Contact;"
    data_contact = {"dbHandle": str(micromsg_handle), "sql": sql_contact}
    
    contact_cache = {}
    response_contact = requests.post(f"{host}/api/db/execSql", headers=headers, json=data_contact)
    result_contact = response_contact.json()
    
    if result_contact.get("Data"):
        for row in result_contact["Data"][1:]:  # 跳过列名
            if len(row) >= 3:
                username = row[0]
                nickname = row[1] if row[1] else username
                remark = row[2] if row[2] else ""
                display_name = remark if remark else nickname
                contact_cache[username] = display_name
    
    # 4. 获取当前最新消息ID
    sql_init = "SELECT MAX(localId) as maxId FROM MSG WHERE StrTalker LIKE '%@chatroom';"
    data_init = {"dbHandle": str(msg_handle), "sql": sql_init}
    response_init = requests.post(f"{host}/api/db/execSql", headers=headers, json=data_init)
    result_init = response_init.json()
    
    last_msg_id = 0
    if result_init.get("Data") and len(result_init["Data"]) > 1:
        for row in result_init["Data"][1:]:
            if row and len(row) > 0 and str(row[0]).isdigit():
                last_msg_id = int(row[0])
                break
    
    print(f"开始监控，当前最新消息ID: {last_msg_id}")
    
    # 5. 监控循环
    while True:
        try:
            # 查询新消息
            sql_new = f"""
            SELECT localId, TalkerId, CreateTime, StrTalker, StrContent, IsSender, Type, BytesExtra
            FROM MSG 
            WHERE localId > {last_msg_id}
            AND StrTalker LIKE '%@chatroom'
            ORDER BY localId ASC;
            """
            
            data_new = {"dbHandle": str(msg_handle), "sql": sql_new}
            response_new = requests.post(f"{host}/api/db/execSql", headers=headers, json=data_new)
            result_new = response_new.json()
            
            if result_new.get("Data") and len(result_new["Data"]) > 1:
                for row in result_new["Data"][1:]:  # 跳过列名
                    if len(row) >= 8 and str(row[0]).isdigit():
                        local_id = int(row[0])
                        create_time = int(row[2]) if str(row[2]).isdigit() else 0
                        group_id = row[3]
                        content = row[4]
                        is_sender = row[5]
                        msg_type = row[6]
                        bytes_extra = row[7]
                        
                        # 格式化时间
                        send_time = datetime.datetime.fromtimestamp(create_time).strftime("%Y-%m-%d %H:%M:%S")
                        
                        # 获取群名称
                        group_name = contact_cache.get(group_id, group_id)
                        
                        # 获取发送者信息（关键部分）
                        if str(is_sender) == "1":
                            sender_name = "我"
                        else:
                            # 从BytesExtra中提取发送者微信ID
                            sender_wxid = extract_sender_wxid(bytes_extra)
                            if sender_wxid:
                                sender_name = contact_cache.get(sender_wxid, sender_wxid)
                            else:
                                sender_name = "未知用户"
                        
                        # 输出JSON格式消息
                        formatted_msg = {
                            "发送人": sender_name,
                            "发送时间": send_time,
                            "群聊名称": group_name,
                            "消息内容": content,
                            "消息类型": str(msg_type),
                            "消息ID": str(local_id)
                        }
                        
                        print(json.dumps(formatted_msg, ensure_ascii=False, indent=2))
                        print("-" * 30)
                        
                        # 更新最新消息ID
                        last_msg_id = local_id
            
            # 等待3秒
            time.sleep(3)
            
        except KeyboardInterrupt:
            print("监控已停止")
            break
        except Exception as e:
            print(f"错误: {e}")
            time.sleep(5)

def extract_sender_wxid(bytes_extra):
    """从BytesExtra中提取发送者微信ID - 核心函数"""
    if not bytes_extra:
        return None
    
    try:
        bytes_str = str(bytes_extra)
        
        # 尝试base64解码
        try:
            decoded = base64.b64decode(bytes_str).decode('utf-8', errors='ignore')
            wxid_pattern = r'wxid_[a-zA-Z0-9]+'
            match = re.search(wxid_pattern, decoded)
            if match:
                return match.group(0)
        except:
            pass
        
        # 直接查找
        wxid_pattern = r'wxid_[a-zA-Z0-9]+'
        match = re.search(wxid_pattern, bytes_str)
        if match:
            return match.group(0)
        
        return None
    except:
        return None

if __name__ == "__main__":
    main()
```

### 关键点总结

1. **BytesExtra解析**：这是获取发送者昵称的核心，必须base64解码
2. **联系人缓存**：预加载所有联系人，避免频繁查询
3. **消息去重**：使用localId递增，只处理新消息
4. **群聊过滤**：StrTalker LIKE '%@chatroom'
5. **昵称优先级**：备注 > 昵称 > 微信ID

### 测试方法

1. 运行程序
2. 在微信群中发送消息
3. 观察输出是否包含真实昵称

### 预期输出

```json
{
  "发送人": "小张",
  "发送时间": "2025-08-19 13:23:03",
  "群聊名称": "测试群",
  "消息内容": "大家好！",
  "消息类型": "1",
  "消息ID": "5815"
}
```

### 常见问题

1. **显示微信ID而非昵称**：检查extract_sender_wxid函数是否正确解析BytesExtra
2. **无法获取消息**：检查数据库句柄和SQL查询
3. **程序崩溃**：添加异常处理，检查网络连接

### 依赖库
```bash
pip install requests
```

这个指南包含了所有关键技术点，下一个AI按照这个模板可以快速实现相同功能。
