#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试微信API接口
"""

import requests
import json
from datetime import datetime, timed<PERSON><PERSON>

def test_api():
    host = "http://127.0.0.1:50007"
    pid = "22504"
    
    # 测试获取会话列表
    print("=== 测试获取会话列表 ===")
    try:
        url = f"{host}/api/session/getSessionList"
        headers = {"X-WeChat-PID": pid}
        response = requests.post(url, headers=headers)
        print(f"状态码: {response.status_code}")
        data = response.json()
        print("响应:")
        print(json.dumps(data, ensure_ascii=False, indent=2))
        
        if data.get("Data"):
            sessions = data["Data"]
            print(f"\n找到 {len(sessions)} 个会话")
            
            # 找一个群聊进行测试
            group_session = None
            for session in sessions:
                if session.get("wxid", "").endswith("@chatroom"):
                    group_session = session
                    break
            
            if group_session:
                print(f"测试群聊: {group_session.get('nickname')} ({group_session.get('wxid')})")
                
                # 测试不同的消息获取接口
                test_group_id = group_session.get('wxid')
                
                print("\n=== 测试 getMsgListByPage ===")
                try:
                    url2 = f"{host}/api/msg/getMsgListByPage"
                    data2 = {
                        "limit": 5,
                        "isSortDesc": True,
                        "wxid": test_group_id
                    }
                    response2 = requests.post(url2, headers=headers, json=data2)
                    print(f"状态码: {response2.status_code}")
                    if response2.status_code == 200:
                        result2 = response2.json()
                        print("响应:")
                        print(json.dumps(result2, ensure_ascii=False, indent=2))
                    else:
                        print(f"错误: {response2.text}")
                except Exception as e:
                    print(f"异常: {e}")
                
                print("\n=== 测试 getMsgList ===")
                try:
                    url3 = f"{host}/api/msg/getMsgList"
                    end_date = datetime.now()
                    start_date = end_date - timedelta(days=1)
                    data3 = {
                        "wxid": test_group_id,
                        "startDate": start_date.strftime("%Y-%m-%d"),
                        "endDate": end_date.strftime("%Y-%m-%d"),
                        "dateType": 2
                    }
                    response3 = requests.post(url3, headers=headers, json=data3)
                    print(f"状态码: {response3.status_code}")
                    if response3.status_code == 200:
                        result3 = response3.json()
                        print("响应:")
                        print(json.dumps(result3, ensure_ascii=False, indent=2))
                    else:
                        print(f"错误: {response3.text}")
                except Exception as e:
                    print(f"异常: {e}")
            
    except Exception as e:
        print(f"异常: {e}")

if __name__ == "__main__":
    test_api()
