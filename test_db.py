#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试微信数据库接口
"""

import requests
import json

def test_db_api():
    host = "http://127.0.0.1:50007"
    pid = "22504"
    headers = {"X-WeChat-PID": pid}
    
    print("=== 测试获取数据库信息 ===")
    try:
        url = f"{host}/api/db/getDBInfo"
        response = requests.post(url, headers=headers)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("响应:")
            print(json.dumps(data, ensure_ascii=False, indent=2))
            
            if data.get("Data"):
                db_info = data["Data"]
                print(f"\n找到 {len(db_info)} 个数据库")
                
                # 查找消息数据库 - 优先使用MSG0.db
                msg_db = None
                for db in db_info:
                    if db.get("databaseName") == "MSG0.db":
                        msg_db = db
                        break

                if not msg_db:
                    # 如果没找到MSG0.db，查找其他消息数据库
                    for db in db_info:
                        if "MSG" in db.get("databaseName", "").upper() or "MESSAGE" in db.get("databaseName", "").upper():
                            msg_db = db
                            break
                
                if msg_db:
                    print(f"\n使用数据库: {msg_db.get('databaseName')} (句柄: {msg_db.get('handle')})")
                    
                    # 测试执行SQL查询
                    print("\n=== 测试执行SQL查询 ===")
                    try:
                        url2 = f"{host}/api/db/execSql"
                        # 先查看有哪些表
                        sql_query = "SELECT name FROM sqlite_master WHERE type='table';"
                        data2 = {
                            "dbHandle": str(msg_db.get('handle')),
                            "sql": sql_query
                        }
                        
                        response2 = requests.post(url2, headers=headers, json=data2)
                        print(f"查询表列表 - 状态码: {response2.status_code}")
                        
                        if response2.status_code == 200:
                            result2 = response2.json()
                            print("表列表:")
                            print(json.dumps(result2, ensure_ascii=False, indent=2))
                            
                            # 如果有MSG表，查询最新的几条消息
                            if result2.get("Data"):
                                tables = [row[0] for row in result2["Data"] if isinstance(row, list) and len(row) > 0]
                                print(f"\n数据库中的表: {tables}")
                                
                                # 查找消息相关的表
                                msg_table = None
                                for table in tables:
                                    if "MSG" in table.upper() or "MESSAGE" in table.upper():
                                        msg_table = table
                                        break
                                
                                if msg_table:
                                    print(f"\n=== 查询消息表 {msg_table} ===")
                                    # 查询表结构
                                    sql_schema = f"PRAGMA table_info({msg_table});"
                                    data3 = {
                                        "dbHandle": str(msg_db.get('handle')),
                                        "sql": sql_schema
                                    }
                                    
                                    response3 = requests.post(url2, headers=headers, json=data3)
                                    if response3.status_code == 200:
                                        schema_result = response3.json()
                                        print("表结构:")
                                        print(json.dumps(schema_result, ensure_ascii=False, indent=2))
                                        
                                        # 查询最新的几条消息
                                        sql_msgs = f"SELECT * FROM {msg_table} ORDER BY CreateTime DESC LIMIT 5;"
                                        data4 = {
                                            "dbHandle": str(msg_db.get('handle')),
                                            "sql": sql_msgs
                                        }
                                        
                                        response4 = requests.post(url2, headers=headers, json=data4)
                                        if response4.status_code == 200:
                                            msgs_result = response4.json()
                                            print("\n最新消息:")
                                            print(json.dumps(msgs_result, ensure_ascii=False, indent=2))
                        else:
                            print(f"查询失败: {response2.text}")
                            
                    except Exception as e:
                        print(f"SQL查询异常: {e}")
        else:
            print(f"获取数据库信息失败: {response.text}")
            
    except Exception as e:
        print(f"异常: {e}")

if __name__ == "__main__":
    test_db_api()
