#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试微信RPC服务连接
"""

import requests
import json

def test_wechat_rpc():
    """测试微信RPC服务连接"""
    host = "http://127.0.0.1:50007"
    
    print("正在测试微信RPC服务连接...")
    print(f"服务地址: {host}")
    print("-" * 40)
    
    try:
        # 测试获取微信客户端列表
        url = f"{host}/api/wechat/list"
        response = requests.get(url, timeout=5)
        
        print(f"请求URL: {url}")
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("响应内容:")
            print(json.dumps(data, ensure_ascii=False, indent=2))
            
            if data.get("Code") == 1 and data.get("Data"):
                clients = data["Data"]
                print(f"\n找到 {len(clients)} 个微信客户端:")
                for i, client in enumerate(clients):
                    print(f"  客户端 {i+1}: PID={client.get('Pid')}")
                return True
            else:
                print("未找到可用的微信客户端")
                return False
        else:
            print(f"请求失败，状态码: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("连接失败：无法连接到微信RPC服务")
        print("请确保：")
        print("1. 微信RPC服务已启动")
        print("2. 服务运行在 127.0.0.1:50007")
        print("3. 防火墙未阻止连接")
        return False
    except requests.exceptions.Timeout:
        print("连接超时：微信RPC服务响应超时")
        return False
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = test_wechat_rpc()
    if success:
        print("\n✅ 微信RPC服务连接正常，可以运行监控程序")
    else:
        print("\n❌ 微信RPC服务连接失败，请检查服务状态")
