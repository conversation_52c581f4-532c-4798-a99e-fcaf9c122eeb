# 微信群消息监控程序技术实现文档

## 项目概述

本项目实现了一个微信群消息监控程序，能够实时监控所有微信群聊的新消息，并以JSON格式输出包含真实发送人昵称的完整消息信息。

## 核心需求

- 监控获取全部群的新消息
- 输出信息包含：发送人昵称、发送时间、群聊名称、消息内容
- 以JSON格式输出
- 实时监控

## 技术架构

### 1. 基础环境
- **微信RPC服务**：运行在 `http://127.0.0.1:50007`
- **数据库访问**：通过RPC接口访问微信本地SQLite数据库
- **主要数据库**：
  - `MSG0.db`：存储消息数据
  - `MicroMsg.db`：存储联系人和群聊信息

### 2. 关键数据库表结构

#### MSG表（MSG0.db）
```sql
-- 消息表，存储所有聊天消息
CREATE TABLE MSG (
    localId INTEGER,        -- 本地消息ID（主键）
    TalkerId INT,          -- 会话ID
    CreateTime INT,        -- 创建时间戳
    StrTalker TEXT,        -- 会话标识（群聊以@chatroom结尾）
    StrContent TEXT,       -- 消息内容
    IsSender INT,          -- 是否自己发送（1=是，0=否）
    Type INT,              -- 消息类型
    BytesExtra BLOB        -- 额外信息（包含发送者微信ID）
);
```

#### Contact表（MicroMsg.db）
```sql
-- 联系人表，存储用户和群聊信息
CREATE TABLE Contact (
    UserName TEXT,         -- 微信ID或群ID
    NickName TEXT,         -- 昵称
    Remark TEXT           -- 备注名
);
```

## 核心技术实现

### 1. 连接微信RPC服务

```python
def get_connection():
    host = "http://127.0.0.1:50007"
    pid = "22504"  # 微信进程ID
    headers = {"X-WeChat-PID": str(pid)}
    
    # 获取数据库句柄
    response = requests.post(f"{host}/api/db/getDBInfo", headers=headers)
    data = response.json()
    
    msg_handle = None
    micromsg_handle = None
    
    for db in data["Data"]:
        if db.get("databaseName") == "MSG0.db":
            msg_handle = db.get("handle")
        elif "MicroMsg" in db.get("databaseName", ""):
            micromsg_handle = db.get("handle")
    
    return host, pid, msg_handle, micromsg_handle
```

### 2. 加载联系人信息

```python
def load_contacts(host, pid, micromsg_handle):
    headers = {"X-WeChat-PID": str(pid)}
    
    sql = "SELECT UserName, NickName, Remark FROM Contact;"
    data = {
        "dbHandle": str(micromsg_handle),
        "sql": sql
    }
    
    contact_cache = {}
    response = requests.post(f"{host}/api/db/execSql", headers=headers, json=data)
    result = response.json()
    
    if result.get("Data"):
        for row in result["Data"][1:]:  # 跳过列名
            if len(row) >= 3:
                username = row[0]
                nickname = row[1] if row[1] else username
                remark = row[2] if row[2] else ""
                
                # 优先使用备注，其次使用昵称
                display_name = remark if remark else nickname
                contact_cache[username] = display_name
    
    return contact_cache
```

### 3. 关键技术：从BytesExtra提取发送者微信ID

**核心难点**：群聊消息的发送者信息不直接存储在明文字段中，而是编码在BytesExtra字段中。

```python
def extract_sender_wxid(bytes_extra):
    """从BytesExtra中提取发送者微信ID"""
    if not bytes_extra:
        return None
    
    try:
        bytes_str = str(bytes_extra)
        
        # 方法1：尝试base64解码
        try:
            decoded = base64.b64decode(bytes_str).decode('utf-8', errors='ignore')
            wxid_pattern = r'wxid_[a-zA-Z0-9]+'
            match = re.search(wxid_pattern, decoded)
            if match:
                return match.group(0)
        except:
            pass
        
        # 方法2：直接在字符串中查找
        wxid_pattern = r'wxid_[a-zA-Z0-9]+'
        match = re.search(wxid_pattern, bytes_str)
        if match:
            return match.group(0)
        
        return None
    except:
        return None
```

**技术说明**：
- BytesExtra字段存储的是base64编码的protobuf数据
- 解码后可以找到类似 `wxid_0f9dl5advg622` 的发送者微信ID
- 使用正则表达式 `r'wxid_[a-zA-Z0-9]+'` 匹配微信ID格式

### 4. 实时监控新消息

```python
def get_new_messages(host, pid, msg_handle, last_msg_id):
    headers = {"X-WeChat-PID": str(pid)}
    
    # 关键SQL：查询比last_msg_id更新的群聊消息
    sql = f"""
    SELECT localId, TalkerId, CreateTime, StrTalker, StrContent, IsSender, Type, BytesExtra
    FROM MSG 
    WHERE localId > {last_msg_id}
    AND StrTalker LIKE '%@chatroom'
    ORDER BY localId ASC;
    """
    
    data = {
        "dbHandle": str(msg_handle),
        "sql": sql
    }
    
    response = requests.post(f"{host}/api/db/execSql", headers=headers, json=data)
    result = response.json()
    
    messages = []
    if result.get("Data"):
        for row in result["Data"][1:]:  # 跳过列名
            if len(row) >= 8 and str(row[0]).isdigit():
                msg = {
                    "localId": int(row[0]),
                    "createTime": int(row[2]) if str(row[2]).isdigit() else 0,
                    "talker": row[3],
                    "content": row[4],
                    "isSender": row[5],
                    "type": row[6],
                    "bytesExtra": row[7]
                }
                messages.append(msg)
    
    return messages
```

### 5. 消息格式化

```python
def format_message(msg, contact_cache):
    # 获取群名称
    group_id = msg.get("talker")
    group_name = contact_cache.get(group_id, group_id)
    
    # 格式化时间
    timestamp = int(msg.get("createTime", 0))
    send_time = datetime.datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S")
    
    # 获取发送者信息
    is_sender = str(msg.get("isSender", 0))
    if is_sender == "1":
        sender_name = "我"
    else:
        # 从BytesExtra中提取发送者微信ID
        bytes_extra = msg.get("bytesExtra")
        sender_wxid = extract_sender_wxid(bytes_extra)
        
        if sender_wxid:
            sender_name = contact_cache.get(sender_wxid, sender_wxid)
        else:
            sender_name = "未知用户"
    
    return {
        "发送人": sender_name,
        "发送时间": send_time,
        "群聊名称": group_name,
        "消息内容": msg.get("content", ""),
        "消息类型": str(msg.get("type", 0)),
        "消息ID": str(msg.get("localId", ""))
    }
```

## 完整实现流程

### 1. 初始化阶段
1. 连接微信RPC服务
2. 获取MSG0.db和MicroMsg.db数据库句柄
3. 加载所有联系人信息到缓存
4. 获取当前最新消息ID作为监控起点

### 2. 监控循环
1. 查询比last_msg_id更新的群聊消息
2. 对每条新消息：
   - 提取发送者微信ID（从BytesExtra）
   - 查找发送者昵称（从联系人缓存）
   - 格式化消息信息
   - 输出JSON格式结果
3. 更新last_msg_id
4. 等待3秒后重复

## 关键技术点

### 1. 数据库访问
- 使用微信RPC服务的 `/api/db/execSql` 接口
- 需要正确的数据库句柄和微信进程ID
- SQL查询结果第一行是列名，需要跳过

### 2. 群聊识别
- 群聊的StrTalker字段以 `@chatroom` 结尾
- 使用 `LIKE '%@chatroom'` 过滤群聊消息

### 3. 发送者识别
- 自己发送的消息：IsSender = 1
- 他人发送的消息：需要从BytesExtra解析微信ID

### 4. 昵称优先级
1. 备注名（Remark）
2. 昵称（NickName）
3. 微信ID（UserName）

## 输出格式

```json
{
  "发送人": "小张",
  "发送时间": "2025-08-19 13:23:03",
  "群聊名称": "测试群",
  "消息内容": "大家好！",
  "消息类型": "1",
  "消息ID": "5815"
}
```

## 依赖库

```python
import requests      # HTTP请求
import json         # JSON处理
import time         # 时间处理
import datetime     # 日期时间格式化
import re           # 正则表达式
import base64       # Base64解码
```

## 注意事项

1. **BytesExtra解析**：这是获取发送者昵称的关键，需要base64解码和正则匹配
2. **联系人缓存**：预加载所有联系人信息，避免频繁查询数据库
3. **消息去重**：使用localId作为唯一标识，避免重复处理
4. **错误处理**：网络请求和数据解析都需要异常处理
5. **性能优化**：3秒轮询间隔，平衡实时性和性能

## 文件结构

- `wechat_nickname_monitor.py` - 最终完整实现
- `test_final_nickname.py` - 测试昵称功能
- `test_connection.py` - 测试连接功能
- `技术实现文档.md` - 本文档
