# 微信群消息监控 - 问题排查指南

## 常见问题及解决方案

### 1. 显示微信ID而不是昵称

**现象**：输出中"发送人"显示为"wxid_xxx"而不是真实昵称

**原因**：BytesExtra解析失败或联系人信息未正确加载

**排查步骤**：

```python
# 测试BytesExtra解析
def debug_bytes_extra():
    # 获取一条消息的BytesExtra
    sql = """
    SELECT BytesExtra FROM MSG 
    WHERE StrTalker LIKE '%@chatroom' 
    AND IsSender = 0 
    ORDER BY CreateTime DESC LIMIT 1;
    """
    
    # 执行查询...
    bytes_extra = result[0]  # 获取到的BytesExtra
    
    print(f"原始BytesExtra: {bytes_extra}")
    
    # 测试解码
    try:
        decoded = base64.b64decode(str(bytes_extra)).decode('utf-8', errors='ignore')
        print(f"解码后: {decoded}")
        
        # 查找微信ID
        import re
        wxid_pattern = r'wxid_[a-zA-Z0-9]+'
        matches = re.findall(wxid_pattern, decoded)
        print(f"找到的微信ID: {matches}")
    except Exception as e:
        print(f"解码失败: {e}")
```

**解决方案**：
1. 确保base64解码正确
2. 检查正则表达式模式
3. 验证联系人缓存是否包含该微信ID

### 2. 无法获取到新消息

**现象**：程序运行但没有输出任何消息

**排查步骤**：

```python
# 检查数据库连接
def test_connection():
    host = "http://127.0.0.1:50007"
    pid = "22504"
    headers = {"X-WeChat-PID": str(pid)}
    
    try:
        response = requests.post(f"{host}/api/db/getDBInfo", headers=headers)
        print(f"连接状态: {response.status_code}")
        print(f"数据库列表: {response.json()}")
    except Exception as e:
        print(f"连接失败: {e}")

# 检查消息查询
def test_message_query():
    sql = "SELECT COUNT(*) FROM MSG WHERE StrTalker LIKE '%@chatroom';"
    # 执行查询...
    print(f"群聊消息总数: {result}")
    
    sql2 = "SELECT MAX(localId) FROM MSG WHERE StrTalker LIKE '%@chatroom';"
    # 执行查询...
    print(f"最新消息ID: {result}")
```

**可能原因**：
1. 微信RPC服务未运行
2. 数据库句柄获取失败
3. last_msg_id设置过高
4. 群聊消息过滤条件有误

### 3. 程序崩溃或异常

**现象**：程序运行时抛出异常

**常见异常及解决**：

```python
# 网络请求异常
try:
    response = requests.post(url, headers=headers, json=data, timeout=10)
except requests.exceptions.RequestException as e:
    print(f"网络请求失败: {e}")
    time.sleep(5)  # 等待后重试

# 数据解析异常
try:
    local_id = int(row[0])
except (ValueError, IndexError) as e:
    print(f"数据解析失败: {e}")
    continue  # 跳过这条数据

# JSON编码异常
try:
    print(json.dumps(formatted_msg, ensure_ascii=False, indent=2))
except UnicodeEncodeError as e:
    print(f"JSON编码失败: {e}")
    # 清理特殊字符后重试
```

### 4. 时间格式错误

**现象**：时间显示为"未知时间"

**排查**：

```python
def debug_timestamp():
    # 检查时间戳格式
    timestamp = row[2]  # CreateTime字段
    print(f"原始时间戳: {timestamp}, 类型: {type(timestamp)}")
    
    if str(timestamp).isdigit():
        timestamp = int(timestamp)
        try:
            dt = datetime.datetime.fromtimestamp(timestamp)
            print(f"转换后时间: {dt}")
        except (ValueError, OSError) as e:
            print(f"时间转换失败: {e}")
```

**解决方案**：
1. 确保时间戳是数字格式
2. 检查时间戳范围是否合理
3. 添加异常处理

### 5. 群聊名称显示为ID

**现象**：群聊名称显示为"123456@chatroom"而不是真实群名

**排查**：

```python
def debug_group_name():
    # 检查Contact表中的群聊信息
    sql = "SELECT UserName, NickName FROM Contact WHERE UserName LIKE '%@chatroom';"
    # 执行查询...
    
    for row in result:
        print(f"群ID: {row[0]}, 群名: {row[1]}")
```

**解决方案**：
1. 确保联系人缓存包含群聊信息
2. 检查群ID是否正确匹配

### 6. 性能问题

**现象**：程序运行缓慢或占用资源过多

**优化方案**：

```python
# 1. 增加查询限制
sql = f"""
SELECT ... FROM MSG 
WHERE localId > {last_msg_id}
AND StrTalker LIKE '%@chatroom'
ORDER BY localId ASC
LIMIT 50;  -- 限制每次查询数量
"""

# 2. 调整轮询间隔
time.sleep(5)  # 从3秒改为5秒

# 3. 优化联系人缓存
# 只加载群聊相关的联系人
sql_contact = """
SELECT UserName, NickName, Remark FROM Contact 
WHERE UserName LIKE '%@chatroom' OR UserName LIKE 'wxid_%';
"""
```

## 调试工具

### 1. 连接测试脚本

```python
def test_all_connections():
    print("=== 微信群消息监控调试工具 ===")
    
    # 测试RPC连接
    print("1. 测试RPC连接...")
    test_connection()
    
    # 测试数据库访问
    print("2. 测试数据库访问...")
    test_message_query()
    
    # 测试联系人加载
    print("3. 测试联系人加载...")
    contact_cache = load_contacts(host, pid, micromsg_handle)
    print(f"加载联系人数量: {len(contact_cache)}")
    
    # 测试BytesExtra解析
    print("4. 测试BytesExtra解析...")
    debug_bytes_extra()
    
    print("调试完成")
```

### 2. 消息格式验证

```python
def validate_message_format(msg):
    """验证消息格式是否正确"""
    required_fields = ["发送人", "发送时间", "群聊名称", "消息内容", "消息类型", "消息ID"]
    
    for field in required_fields:
        if field not in msg:
            print(f"缺少字段: {field}")
            return False
    
    # 检查时间格式
    time_pattern = r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}'
    if not re.match(time_pattern, msg["发送时间"]):
        print(f"时间格式错误: {msg['发送时间']}")
        return False
    
    return True
```

## 环境检查清单

运行程序前请确认：

- [ ] 微信客户端已启动并登录
- [ ] 微信RPC服务运行在127.0.0.1:50007
- [ ] Python已安装requests库
- [ ] 有可访问的微信群聊
- [ ] 微信进程ID正确（通常是22504）

## 日志记录建议

```python
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('wechat_monitor.log'),
        logging.StreamHandler()
    ]
)

# 在关键位置添加日志
logging.info(f"加载了 {len(contact_cache)} 个联系人")
logging.info(f"开始监控，最新消息ID: {last_msg_id}")
logging.error(f"BytesExtra解析失败: {e}")
```

## 快速验证脚本

```python
#!/usr/bin/env python3
"""快速验证脚本 - 测试所有核心功能"""

def quick_test():
    try:
        # 1. 测试连接
        host, pid, msg_handle, micromsg_handle = get_db_handles()
        print("✅ 数据库连接成功")
        
        # 2. 测试联系人加载
        contact_cache = load_contacts(host, pid, micromsg_handle)
        print(f"✅ 加载联系人: {len(contact_cache)}个")
        
        # 3. 测试消息查询
        messages = get_new_messages(host, pid, msg_handle, 0)
        print(f"✅ 查询到消息: {len(messages)}条")
        
        # 4. 测试消息格式化
        if messages:
            formatted = format_message(messages[0], contact_cache)
            print("✅ 消息格式化成功")
            print(json.dumps(formatted, ensure_ascii=False, indent=2))
        
        print("🎉 所有功能测试通过")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    quick_test()
```

使用这个排查指南，下一个AI可以快速定位和解决实现过程中遇到的问题。
